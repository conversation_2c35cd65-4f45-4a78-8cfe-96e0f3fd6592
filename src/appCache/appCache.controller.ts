import { Controller, Delete, Get, HttpStatus, Param, Post } from '@nestjs/common';
import { Interval } from '@nestjs/schedule';
import { ApiTags } from '@nestjs/swagger';
import { LogLevel } from 'ads-layouts-tools';
import { cachePrefix } from 'InterfacesAndTypes';
import { CreateException, log } from 'Utils';
import { CacheService } from '../cacheModule/cache.service';
import { DisplayConfigService } from '../displayConfig/displayConfig.service';
import { ENV } from '../envalidConfig';
import { cacheGet } from '../localCache/cacheGet';
import { cacheAddToArray } from '../localCache/cacheSet';
import { AppCacheService } from './appCache.service';
import { CachePrefixValidatorPipe } from './cachePrefixQuery';

@Controller('appCache')
@ApiTags('AppCache')
export class AppCacheController {
  constructor(
    private readonly appCacheService: AppCacheService,
    private readonly displayConfigService: DisplayConfigService,
    private readonly cache: CacheService
  ) {}

  async onModuleInit(): Promise<void> {
    await this.updateServiceIds();
  }

  @Interval(ENV.UPDATE_SERVICE_ID_INTERVAL)
  @Post('/updateServiceIds')
  async updateServiceIds(): Promise<void> {
    try {
      const serviceIds = await this.displayConfigService.getUniqServiceIds();

      await this.cache.setServiceIds(serviceIds);
    } catch (error) {
      throw CreateException({
        message: error?.message,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR
      });
    }
  }

  @Interval(ENV.LOCAL_CACHE_RESET_CHECK)
  @Get('/update')
  async updateAppCache(): Promise<void> {
    try {
      log('APP_CACHE_RESET_CHECK_INVOKED', {}, LogLevel.cache);
      const executedResets = cacheGet<number[]>('executedResets');

      log('APP_CACHE_EXECUTED_RESETS_TIMESTAMPS', { executedResets }, LogLevel.cache);

      const allResetsToExecute = await this.appCacheService.getWorkerCacheConfig();

      log('APP_CACHE_ALL_SCHEDULED_RESETS', { allResetsToExecute }, LogLevel.cache);

      const resetsToExecute = allResetsToExecute.filter(
        r => !executedResets.includes(r.timestamp)
      );

      log('APP_CACHE_RESETS_SELECTED_TO_EXECUTE', { resetsToExecute }, LogLevel.cache);

      for (const config of resetsToExecute) {
        const prefix: cachePrefix = `${config.cachePart}__${config.serviceId}`;
        log('APP_CACHE_PREFIX_TO_RESET', { prefix }, LogLevel.cache);

        const delStatus = await this.cache.deleteByPrefix(prefix);
        if (delStatus > 0) {
          cacheAddToArray('executedResets', config.timestamp);
        }
      }

      log(
        'APP_CACHE_CHECK_EXECUTED_RESETS',
        { executedResets: cacheGet<number[]>('executedResets') },
        LogLevel.cache
      );

      await this.cache.reCalculateCacheHash();
    } catch (error) {
      throw CreateException({
        message: error?.message,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR
      });
    }
  }

  @Get('/getAllKeys')
  async getAllKeys(): Promise<string[]> {
    try {
      return this.cache.getKeys();
    } catch (error) {
      throw CreateException({
        message: error?.message,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR
      });
    }
  }

  @Get('/countKeys')
  async countKeys(): Promise<number> {
    try {
      const keys = await this.cache.getKeys();
      return keys.length;
    } catch (error) {
      throw CreateException({
        message: error?.message,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR
      });
    }
  }

  @Delete('/deleteAllKeys')
  async resetAllKeys(): Promise<string> {
    try {
      await this.cache.resetStore();
      return 'All keys deleted';
    } catch (error) {
      throw CreateException({
        message: error?.message,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR
      });
    }
  }

  @Delete('/deleteByPrefix/:prefix')
  async deleteByPrefix(
    @Param('prefix', CachePrefixValidatorPipe) prefix: cachePrefix
  ): Promise<{ status: string }> {
    try {
      const countKeysDeleted = await this.cache.deleteByPrefix(prefix);

      return countKeysDeleted === 0
        ? { status: `No keys with prefix ${prefix} found` }
        : { status: `All keys with prefix ${prefix} deleted` };
    } catch (error) {
      throw CreateException({
        message: error?.message,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR
      });
    }
  }

  @Get('/getCacheDump')
  async getCacheDump(): Promise<[string, { ttl: number; start: number }][]> {
    return this.cache.getCacheDump();
  }
}
