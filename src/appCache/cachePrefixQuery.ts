import { HttpStatus, PipeTransform } from '@nestjs/common';
import { CachePartsEnum } from 'ads-layouts-tools';
import { cachePrefix } from 'InterfacesAndTypes';
import * as joi from 'joi';
import { JoiSchemaOptions } from 'nestjs-joi';
import { CreateException } from 'Utils';

const possiblePrefixes = Object.values(CachePartsEnum).join('|');

const cachePrefixValidator = joi
  .string()
  .pattern(new RegExp(`^(${possiblePrefixes})__[0-9a-zA-Z]+$`));

@JoiSchemaOptions({})
export class CachePrefixValidatorPipe implements PipeTransform<cachePrefix> {
  transform(value: string): cachePrefix {
    const result = cachePrefixValidator.validate(value);
    if (result.error) {
      throw CreateException({
        message: `Invalid cache prefix: ${result.error.message}`,
        statusCode: HttpStatus.BAD_REQUEST
      });
    }
    return value as cachePrefix;
  }
}
