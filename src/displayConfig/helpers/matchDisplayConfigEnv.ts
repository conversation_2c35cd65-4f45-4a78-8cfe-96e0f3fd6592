import { CustomHttpStatus, DisplayConfig, LogLevel } from 'ads-layouts-tools';
import { CreateException, log } from 'Utils';

export const matchDisplayConfigEnv = (
  envToMatch = 'default',
  displayConfigs: DisplayConfig[],
  serviceId?: string
): DisplayConfig[] => {
  log(
    'SELECT_RELEASE_VERSION_MATCH_ENV',
    {
      envToMatch,
      displayConfigs: displayConfigs.map(el => ({
        service: el.service,
        env: el.env,
        siteVersion: el.siteVersion,
        release: el.release
      }))
    },
    LogLevel.info
  );

  if (!displayConfigs?.length) {
    log(
      `WARN_${CustomHttpStatus.CANNOT_FIND_ANY_RELEASE}_CANNOT_FIND_ANY_RELEASE_FOR_ENV`,
      { envToMatch, serviceId, displayConfigs },
      LogLevel.warn
    );

    throw CreateException({
      message: `Lack of displayConfig for serviceId: ${serviceId}, env: ${envToMatch}`,
      statusCode: CustomHttpStatus.CANNOT_FIND_ANY_RELEASE
    });
  }

  const exactMatchFind = displayConfigs.filter(dc => dc.env === envToMatch);

  if (exactMatchFind.length) {
    return exactMatchFind;
  }

  if (envToMatch.includes('-')) {
    const splitedEnvToMatch = envToMatch.split('-');
    const [envPrefix, envVersion] = splitedEnvToMatch;

    const findEnv = displayConfigs.filter(dc =>
      [envPrefix, '-', envVersion].every(el => dc.env.includes(el))
    );

    if (findEnv.length) {
      return findEnv;
    }
  }

  // return default config if cannot match
  return displayConfigs.filter(dc => dc.env === 'default');
};
