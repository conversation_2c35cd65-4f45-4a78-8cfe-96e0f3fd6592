import { HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  CachePartsEnum,
  DisplayConfig,
  DisplayConfigDocument,
  LogLevel
} from 'ads-layouts-tools';
import * as dayjs from 'dayjs';
import { deHydrateDocuments } from 'Helpers';
import {
  DisplayConfigKey,
  ReleaseWithTimestamp,
  selectReleaseVersionArgs
} from 'InterfacesAndTypes';
import { Model } from 'mongoose';
import { CreateException, log } from 'Utils';
import { CacheService } from '../cacheModule/cache.service';
import { matchDisplayConfigEnv } from './helpers/matchDisplayConfigEnv';

@Injectable()
export class DisplayConfigService {
  constructor(
    @InjectModel(DisplayConfig.name)
    private DisplayConfigModel: Model<DisplayConfigDocument>,
    private readonly cache: CacheService
  ) {}

  async getUniqServiceIds(): Promise<string[]> {
    const uniqServiceIds: string[] = await this.DisplayConfigModel.distinct('service');
    if (!uniqServiceIds || uniqServiceIds.length == 0) {
      throw CreateException({
        message: 'DisplayConfigs data not found!',
        statusCode: HttpStatus.NOT_FOUND
      });
    }
    return uniqServiceIds;
  }

  async selectReleaseVersion(
    args: selectReleaseVersionArgs,
    omitCache = false
  ): Promise<string | undefined> {
    const { serviceId, time, serviceEnv, siteVersion } = args;

    const appCacheKey: DisplayConfigKey = `${CachePartsEnum.RELEASE}__${serviceId}__${time}`;

    log(
      'SELECT_RELEASE_VERSION_FOR_PARAMS',
      {
        serviceId,
        serviceEnv,
        siteVersion,
        time,
        timeToDate: dayjs(parseInt(time)).format('YYYY-MM-DDTHH:mm:ss:SSSZ'),
        appCacheKey,
        omitCache
      },
      LogLevel.dev
    );

    const allDisplayConfigsForEnv = await this.selectDisplayConfigs(
      appCacheKey,
      serviceId,
      serviceEnv,
      omitCache
    );

    // filter for env

    log(
      'SELECT_RELEASE_VERSION_MATCHED_ENVS',
      {
        data: allDisplayConfigsForEnv.map(el => ({
          service: el.service,
          env: el.env,
          siteVersion: el.siteVersion,
          release: el.release
        }))
      },
      LogLevel.dev
    );

    const releaseFromSiteVersion = this.getReleaseForSiteVersions(
      allDisplayConfigsForEnv,
      siteVersion
    );
    if (releaseFromSiteVersion) {
      return releaseFromSiteVersion;
    }

    // siteVersion contains AFTER prefix
    if (time) {
      const configsAfterTimestamp = this.findConfigsAfterRequestTimestamp(
        allDisplayConfigsForEnv,
        time
      );
      if (configsAfterTimestamp) {
        return configsAfterTimestamp;
      }
    }

    // empty siteVersion provided
    const defaultConfig = allDisplayConfigsForEnv.find(c =>
      ['default', 'RELEASE'].includes(c.siteVersion)
    );

    log('SELECT_RELEASE_VERSION_DEFAULT_SCENARIO', { config: defaultConfig }, LogLevel.dev);

    return defaultConfig?.release;
  }

  getReleaseForSiteVersions(
    displayConfigsForEnv: DisplayConfig[],
    siteVersion: string | undefined
  ): string | undefined {
    if (!siteVersion) {
      return;
    }

    const isMultipleSiteVersionProvided = siteVersion.includes(',');

    // siteVersion other than empty or default provided
    if (!isMultipleSiteVersionProvided) {
      const selectedSiteVersion = displayConfigsForEnv.find(
        c => c.siteVersion === siteVersion
      );

      if (selectedSiteVersion) {
        log(
          'SELECT_RELEASE_VERSION_SINGLE_SITE_VERSION_SCENARIO',
          { selectedSiteVersion },
          LogLevel.dev
        );
        return selectedSiteVersion.release;
      } else {
        return;
      }
    } else {
      const requestSiteVersionSet = new Set(siteVersion.split(','));

      const potentialReleases: Map<number, DisplayConfig> = new Map();

      for (const config of displayConfigsForEnv.reverse()) {
        let matchingVersionCount = 0;
        const configVersionArr = config.siteVersion.split(',');

        for (const configVersion of configVersionArr) {
          if (requestSiteVersionSet.has(configVersion)) {
            matchingVersionCount++;
          } else {
            matchingVersionCount = -1;
            break;
          }
        }

        if (matchingVersionCount > 0) {
          potentialReleases.set(matchingVersionCount, config);
        }
      }

      if (potentialReleases.size === 0) {
        return;
      }

      const bestMatch = potentialReleases.get(Math.max(...potentialReleases.keys()));
      log(
        'SELECT_RELEASE_VERSION_MULTIPLE_SITE_VERSION_SCENARIO',
        { config: bestMatch },
        LogLevel.dev
      );
      return bestMatch!.release;
    }
  }

  async selectDisplayConfigs(
    appCacheKey: DisplayConfigKey,
    serviceId: string,
    serviceEnv?: string,
    omitCache = false
  ): Promise<DisplayConfig[]> {
    let displayConfigDocuments = await this.cache.get(appCacheKey, omitCache);

    if (!displayConfigDocuments) {
      log('SELECT_RELEASE_VERSION_FETCH_FROM_DB', { appCacheKey });
      displayConfigDocuments = await this.DisplayConfigModel.find({
        service: serviceId
      });

      if (displayConfigDocuments.length > 0) {
        await this.cache.set(appCacheKey, displayConfigDocuments, omitCache);
      }
    }

    const displayConfigs = deHydrateDocuments(displayConfigDocuments);

    return matchDisplayConfigEnv(serviceEnv, displayConfigs, serviceId);
  }

  findConfigsWithSiteVersionAFTER(
    allDisplayConfigsForEnv: DisplayConfig[]
  ): ReleaseWithTimestamp[] | undefined {
    const regexPattern = /^AFTER:(?<afterTimestamp>.+)/;

    const filteredReleasesWithTimestamp = allDisplayConfigsForEnv.reduce(
      (acc: ReleaseWithTimestamp[], curr: DisplayConfig) => {
        const result = curr.siteVersion.match(regexPattern);
        if (result?.groups) {
          acc.push({
            release: curr.release,
            timestamp: dayjs(result.groups.afterTimestamp).valueOf()
          });
        }
        return acc;
      },
      []
    );

    return filteredReleasesWithTimestamp.length !== 0
      ? filteredReleasesWithTimestamp.sort((a, b) => a.timestamp - b.timestamp).reverse()
      : undefined;
  }
  findConfigsAfterRequestTimestamp(
    allDisplayConfigsForEnv: DisplayConfig[],
    time: string
  ): string | undefined {
    const configsSortedByTimestamp =
      this.findConfigsWithSiteVersionAFTER(allDisplayConfigsForEnv);

    const requestIntTimestamp = parseInt(time);
    const configAfterRequestTimestamp = configsSortedByTimestamp?.find(c => {
      log(
        'SELECT_RELEASE_VERSION_FIND_CORRECT_AFTER',
        {
          ['AFTER date']: dayjs(c.timestamp).format('YYYY-MM-DDTHH:mm:ss:SSSZ'),
          ['REQUEST date']: dayjs(requestIntTimestamp).format('YYYY-MM-DDTHH:mm:ss:SSSZ'),
          afterTimestamp: c.timestamp,
          requestTimestamp: requestIntTimestamp,
          requestTimestampBiggerThanAfterTimestamp: requestIntTimestamp > c.timestamp
        },
        LogLevel.dev
      );

      return c.timestamp < requestIntTimestamp;
    });

    log(
      'SELECT_RELEASE_VERSION_FIND_CORRECT_AFTER_RESULT',
      {
        result: configAfterRequestTimestamp ? 'SUCCESS' : 'FAIL',
        timeStamp: configAfterRequestTimestamp?.timestamp || 'n/a',
        date: dayjs(configAfterRequestTimestamp?.timestamp).format('YYYY-MM-DDTHH:mm:ss:SSSZ'),
        release: configAfterRequestTimestamp?.release || 'n/a'
      },
      LogLevel.dev
    );

    return configAfterRequestTimestamp?.release;
  }
}
