import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  CachePartsEnum,
  LogLevel,
  ServiceToPackageMap,
  ServiceToPackageMapDocument
} from 'ads-layouts-tools';
import { deHydrateDocument } from 'Helpers';
import { ContentMeta, RulesPackageKey } from 'InterfacesAndTypes';
import { Model } from 'mongoose';
import { log } from 'Utils';
import { CacheService } from '../cacheModule/cache.service';

@Injectable()
export class ServiceToPackageMapService {
  constructor(
    @InjectModel(ServiceToPackageMap.name)
    private serviceToPackageMapModel: Model<ServiceToPackageMapDocument>,
    private readonly cache: CacheService
  ) {}

  async getRulesPackageForService(
    contentMeta: ContentMeta,
    omitCache = false
  ): Promise<string | undefined> {
    const { serviceId, rulesPackage } = contentMeta;

    const key: RulesPackageKey = `${CachePartsEnum.RULES_PACKAGE}__${serviceId}`;
    let serviceToPackageMapDocument = await this.cache.get(key, omitCache);

    if (!serviceToPackageMapDocument) {
      serviceToPackageMapDocument = await this.serviceToPackageMapModel.findOne({
        serviceId
      });

      if (serviceToPackageMapDocument) {
        await this.cache.set(key, serviceToPackageMapDocument, omitCache);
      }

      log(
        'RULES_PACKAGE_ASSIGNED_TO_SERVICE_GET_FROM_DB',
        { serviceId, exist: !!serviceToPackageMapDocument },
        LogLevel.dev
      );
    }

    if (!serviceToPackageMapDocument && !rulesPackage) {
      log('WARNING_NO_RULES_PACKAGE_ASSIGNED_TO_SERVICE', { serviceId }, LogLevel.warn);
    }

    const result =
      (serviceToPackageMapDocument &&
        deHydrateDocument(serviceToPackageMapDocument)?.rulesPackage) ||
      rulesPackage;

    return result;
  }
}
