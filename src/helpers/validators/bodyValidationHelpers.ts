import { AccessModelEnum, LogLevel, PaywallEnum } from 'ads-layouts-tools';
import { log } from 'Utils';

export const validateAccessModel = () => {
  const allowed = Object.values(AccessModelEnum);

  return (passedValue: string) => {
    const passedValueIsNotAllowed = !allowed.includes(passedValue as any);

    if (passedValueIsNotAllowed) {
      log('ACCESS_MODEL_VALIDATION_FAILED', { passedValue }, LogLevel.warn);
    }

    return passedValue;
  };
};

export const validatePaywall = () => {
  const allowed = Object.values(PaywallEnum);

  return (passedValue: string) => {
    const passedValueIsNotAllowed = !allowed.includes(passedValue as any);

    if (passedValueIsNotAllowed) {
      log('PAYWALL_VALIDATION_FAILED', { passedValue }, LogLevel.warn);
    }

    return passedValue;
  };
};
