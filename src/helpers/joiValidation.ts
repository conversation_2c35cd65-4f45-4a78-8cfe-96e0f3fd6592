import {
  BodyToHeaderParse,
  ContentMeta,
  ContentMetaOptionalFields,
  ContentMetaRequiredFields
} from 'InterfacesAndTypes';
import * as joi from 'joi';

export type StringValidationType<T> = Record<keyof T, joi.StringSchema>;

export const requiredHeaders: Array<keyof BodyToHeaderParse<ContentMetaRequiredFields>> = [
  'x-at-device-type',
  'x-at-location-info-page-type',
  'x-at-location-info-section-id',
  'x-at-location-info-section-name',
  'x-at-service-id',
  'x-at-site-version-identifier',
  'x-at-time'
];

export const optionalHeaders: Array<keyof BodyToHeaderParse<ContentMetaOptionalFields>> = [
  'x-at-access-model',
  'x-at-paywall',
  'x-at-rules-package',
  'x-at-service-env',
  'x-at-site-version',
  'x-at-location-info-page-id'
];

export function requireStringValues<T>(listOfKeys: Array<keyof T>): StringValidationType<T> {
  return listOfKeys.reduce((objectValue: StringValidationType<T>, currentValue) => {
    objectValue[currentValue] = joi.string().required().allow('');

    return objectValue;
  }, {} as StringValidationType<T>);
}

export const kebabCaseToCamelCase = (input: string): string =>
  input
    .replace(/^x-at-/, '')
    .replace(/-([a-z])/g, (match, group1: string) => group1.toUpperCase());

export function createHeaderSchema<T>(
  headers: Array<keyof BodyToHeaderParse<T> & string>,
  bodyMeta: T,
  isOptional: boolean
): StringValidationType<BodyToHeaderParse<T>> {
  return headers.reduce<StringValidationType<BodyToHeaderParse<T>>>(
    (headerSchema, header) => {
      const headerMetaKey = kebabCaseToCamelCase(header) as keyof T;

      if (bodyMeta[headerMetaKey] === undefined && isOptional) {
        headerSchema[header] = joi
          .string()
          .forbidden()
          .messages({
            'any.unknown': `Optional "${header}" was not specified in the body meta input`
          });

        return headerSchema;
      }

      const headerMetaValue = encodeURIComponent(String(bodyMeta[headerMetaKey]));

      headerSchema[header] = joi.string().valid(headerMetaValue).required();

      return headerSchema;
    },
    {} as StringValidationType<BodyToHeaderParse<T>>
  );
}
export const createHeadersWithValuesSchema = <T extends ContentMeta>(
  meta: T
): StringValidationType<BodyToHeaderParse<T>> => ({
  ...createHeaderSchema(requiredHeaders, meta, false),
  ...createHeaderSchema(optionalHeaders, meta, true)
});
