import { All, Controller, HttpStatus, Req, UseGuards } from '@nestjs/common';
import { LogLevel } from 'ads-layouts-tools';
import { FastifyRequest } from 'fastify';
import { CreateException, log } from 'Utils';
import { LoggingThrottlerGuard } from './customThrottlerGuard.service';

@Controller('/*')
export class BlacklistController {
  @UseGuards(LoggingThrottlerGuard)
  @All()
  allNonExplicitEndpoints(@Req() req: FastifyRequest) {
    log(
      'FORBIDDEN_PATH',
      {
        IP: req.ip,
        Path: req.url,
        Headers: JSON.stringify(req.headers)
      },
      LogLevel.dev
    );

    throw CreateException({
      message: 'Forbidden path',
      statusCode: HttpStatus.FORBIDDEN
    });
  }
}
