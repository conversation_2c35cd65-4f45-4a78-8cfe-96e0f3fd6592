import { Controller, Get, HttpStatus } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { IAppInfo } from 'InterfacesAndTypes';
import { CreateException } from 'Utils';
import { InfoService } from './info.service';

@Controller('check')
@ApiTags('Info')
export class InfoController {
  constructor(private infoService: InfoService) {}

  @Get('/app')
  appInfo(): IAppInfo {
    try {
      return this.infoService.appInfo;
    } catch (error) {
      throw CreateException({
        message: error?.message,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR
      });
    }
  }

  @Get('/')
  appCheck(): string {
    try {
      return this.infoService.healthCheck();
    } catch (error) {
      throw CreateException({
        message: error?.message,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR
      });
    }
  }

  @Get('/version')
  appVersion(): string {
    try {
      return this.infoService.appVersion;
    } catch (error) {
      throw CreateException({
        message: error?.message,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR
      });
    }
  }
}
