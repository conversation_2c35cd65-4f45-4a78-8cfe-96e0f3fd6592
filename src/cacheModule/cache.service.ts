import { <PERSON><PERSON>, CACHE_MANAGER, CACHE_MODULE_OPTIONS } from '@nestjs/cache-manager';
import { HttpStatus, Inject, Injectable } from '@nestjs/common';
import { LogLevel } from 'ads-layouts-tools';
import {
  AppCacheKeyType,
  CacheID,
  cachePrefix,
  KeyToDataType,
  ModuleOptions
} from 'InterfacesAndTypes';
import { sha1 } from 'utility';
import { CreateException, log } from 'Utils';
import { ENV } from '../envalidConfig';

const ServiceIdKey = 'serviceIds' as const;

@Injectable()
export class CacheService {
  constructor(
    @Inject(CACHE_MODULE_OPTIONS) private readonly options: ModuleOptions,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache
  ) {}

  private currentCacheHash: string | null = null;

  /**
   * Replaces periods and slashes in the input key with underscores.
   *
   * @param key - The key to be normalized
   * @returns The normalized key with periods and slashes replaced by underscores
   */
  private normalizeKey<T extends string>(key: T): T {
    return key.replace(/[\.\/]/g, '_') as T;
  }

  /**
   * Generates a unique cache identifier for the given key.
   *
   * @param key - The cache key to generate an identifier for.
   * @returns A unique CacheID string composed of the normalized key and its SHA1 hash.
   */
  private makeId<K extends AppCacheKeyType>(key: K): CacheID {
    const normalizedKey = this.normalizeKey(key);
    return `${normalizedKey}__${sha1(normalizedKey)}`;
  }

  /**
   * Counts the number of items in the given data, or returns 0/1
   * if the data is not an array.
   *
   * @param data data to count
   * @returns the count of items in the data
   */
  private getCount<K extends AppCacheKeyType>(data: KeyToDataType[K]): number {
    return Array.isArray(data) ? data.length : !data ? 0 : 1;
  }

  /**
   * Retrieve cached data for a given key
   *
   * @param appCacheKey cache ID to retrieve
   * @param omitCache is cache intentionally omitted using query param
   * @returns an array of ServiceToPackageMap objects, or an empty array if no data was found
   */
  public async get<K extends AppCacheKeyType>(
    appCacheKey: K,
    omitCache = false
  ): Promise<KeyToDataType[K] | null> {
    if (omitCache) {
      log('GET_CACHE_OMITTED_USING_QUERY_PARAM', { appCacheKey, omitCache }, LogLevel.cache);

      return null;
    }

    if (!this.options.isActive) {
      return null;
    }

    const cacheId = this.makeId(appCacheKey);
    const result = await this.cacheManager.get<KeyToDataType[K]>(cacheId);

    if (result) {
      log('CACHE_DATA_FOUND', { cacheId }, LogLevel.cache);

      return result;
    }

    log('CACHE_NO_DATA', { cacheId }, LogLevel.warn);

    return null;
  }

  /**
   * Sets the value for a given key in the cache.
   * If the ttl (time to live) is set to a value greater than 0,
   * the value will be expired after that amount of milliseconds.
   *
   * @param appCacheKey cache ID to set
   * @param data the data to set for the given key
   * @param ttl (optional) the time to live for the cached data in milliseconds. If not provided, the data will not expire.
   * @param omitCache is cache intentionally omitted using query param
   * @returns a promise that resolves when the cache entry has been set
   */
  public async set<K extends AppCacheKeyType>(
    appCacheKey: K,
    data: KeyToDataType[K],
    omitCache = false,
    ttl = ENV.LOCAL_CACHE_TTL
  ): Promise<void> {
    if (omitCache) {
      log('SET_CACHE_OMITTED_USING_QUERY_PARAM', { appCacheKey, omitCache }, LogLevel.cache);

      return;
    }

    if (!this.options.isActive) {
      return;
    }

    const cacheId = this.makeId(appCacheKey);

    await this.cacheManager.set(cacheId, data, ttl);

    log('CACHE_DATA_SAVED', { cacheId, length: this.getCount(data) }, LogLevel.cache);

    this.reCalculateCacheHash();
  }

  /**
   * Deletes the cached data for a given key.
   *
   * @param appCacheKey the cache ID to delete
   * @returns a promise that resolves when the cache entry has been deleted
   */
  public async del(appCacheKey: AppCacheKeyType): Promise<void> {
    if (!this.options.isActive) {
      return;
    }

    const cacheId = this.makeId(appCacheKey);

    await this.cacheManager.del(cacheId);

    log('CACHE_PARTIALLY_CLEARED', { cacheId }, LogLevel.cache);
  }

  /**
   * Gets all keys in the cache.
   *
   * @returns a promise that resolves to an array of all keys in the cache
   */
  public getKeys(): Promise<string[]> {
    return this.cacheManager.store.keys();
  }

  /**
   * Deletes all cache entries.
   *
   * @returns a promise that resolves once the cache has been cleared
   */
  public async resetStore(): Promise<any> {
    await this.cacheManager.store.reset();
  }

  /**
   * Deletes all cache entries that start with the given prefix.
   *
   * @param prefix the prefix to filter cache entries by
   * @returns the number of cache entries that were deleted
   */
  public async deleteByPrefix(prefix: cachePrefix): Promise<number> {
    const allKeys = await this.getKeys();
    const keysFilteredByPrefix = allKeys.filter(k => k.startsWith(prefix));

    for (const key of keysFilteredByPrefix) {
      await this.cacheManager.del(key);
    }

    return keysFilteredByPrefix.length;
  }

  /**
   * Sets the service IDs in the cache with a defined time-to-live (TTL).
   *
   * @param serviceIds - An array of service IDs to be cached.
   * @returns A promise that resolves when the service IDs are successfully set in the cache.
   */

  public async setServiceIds(serviceIds: string[]): Promise<void> {
    log('APP_CACHE_SET_SERVICE_IDS_FOR_VALIDATION', { serviceIds }, LogLevel.cache);

    await this.cacheManager.set(ServiceIdKey, serviceIds, ENV.UPDATE_SERVICE_ID_TTL);
  }

  /**
   * Gets the service IDs from the cache.
   *
   * @returns a promise that resolves to an array of service IDs, or undefined if no service IDs are cached
   */
  public getServiceIds(): Promise<string[] | undefined> {
    return this.cacheManager.get<string[]>(ServiceIdKey);
  }

  /**
   * Gets the dump of the current cache store.
   *
   * @returns dump of the current cache store.
   */
  public getCacheDump(): [string, { ttl: number; start: number }][] {
    const memoryStore = this.cacheManager.store as any;

    if (typeof memoryStore?.dump === 'function') {
      return memoryStore.dump();
    } else {
      throw CreateException({
        message: 'Cache dump is not available for the current cache store',
        statusCode: HttpStatus.BAD_REQUEST
      });
    }
  }

  /**
   * Calculate hash from the dump of the current cache store.
   * Results are stored in the currentCacheHash property.
   */
  public reCalculateCacheHash() {
    const currentDump: [string, { ttl: number; start: number }][] = this.getCacheDump();

    const dumpForHash = currentDump.reduce<string>((prev, [key, value]) => {
      if (typeof value?.ttl === 'number' && typeof value?.start === 'number') {
        prev += `${key} ${value.ttl} ${value.start} | `;
      }

      return prev;
    }, '');

    this.currentCacheHash = sha1(`${process.env.UNIQUE_PROCESS_ID}_${dumpForHash}`);

    log(
      'CACHE_HASH_RECALCULATED',
      { currentCacheHash: this.currentCacheHash },
      LogLevel.cache
    );
  }

  /**
   * Create and return headers for response
   *
   * @returns headers for response
   */
  public getAppCacheHeaders(): { [key: string]: string | undefined } {
    const collectionsCacheTtl: Record<string, number> = this.getCollectionsCacheTtl();
    const collectionsCacheTtlHeaders: Record<string, number> = {};

    Object.keys(collectionsCacheTtl).forEach(key => {
      collectionsCacheTtlHeaders[`x-alac-${key.toLowerCase()}`] = collectionsCacheTtl[key];
    });

    return {
      ...collectionsCacheTtlHeaders,
      'x-alac-hash': this.currentCacheHash || 'app-cache-not-set-yet',
      'x-ads-layouts-unique-process-id': process.env.UNIQUE_PROCESS_ID
    };
  }

  /**
   * Get TTL from collection using cacheDump object
   *
   * @returns object representing TTL for cache parts
   */
  private getCollectionsCacheTtl(): Record<string, number> {
    const currentDump: [string, { ttl: number; start: number }][] = this.getCacheDump();
    const ttlPerCollection: Record<string, number> = {};

    currentDump.forEach(([key, value]: [string, { ttl: number; start: number }]) => {
      ttlPerCollection[`${key.split('__')[0]}-TTL`] = value?.ttl;
    });

    return ttlPerCollection;
  }
}
