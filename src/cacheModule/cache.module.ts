import { CACHE_MODULE_OPTIONS, CacheModule as NestJsCacheModule } from '@nestjs/cache-manager';
import { DynamicModule, Global, Module, Provider } from '@nestjs/common';
import { LogLevel } from 'ads-layouts-tools';
import { ModuleOptions } from 'InterfacesAndTypes';
import { log } from 'Utils';
import { ENV } from '../envalidConfig';
import { CacheService } from './cache.service';

@Global()
@Module({})
export class CacheModule {
  static register(options: ModuleOptions): DynamicModule {
    const { isActive } = options;
    const providers: Provider[] = [];

    providers.push(CacheService);
    providers.push({
      provide: CACHE_MODULE_OPTIONS,
      useValue: { isActive }
    });

    const cacheParams = {
      isGlobal: true,
      ttl: isActive ? ENV.LOCAL_CACHE_TTL : 1,
      max: isActive ? ENV.LOCAL_CACHE_MAX_ITEMS_AMOUNT : 1,
      store: isActive ? 'memory' : undefined
    };

    log('CACHE_MODULE_REGISTER', { ...cacheParams }, LogLevel.info);

    const imports = [NestJsCacheModule.register(cacheParams)];

    return {
      module: CacheModule,
      imports,
      providers,
      exports: providers
    };
  }
}
