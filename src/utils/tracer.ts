import tracer from 'dd-trace';
import { BodyToHeaderParse, ContentMeta } from 'InterfacesAndTypes';

tracer.init({
  logInjection: true
}); // initialized in a different file to avoid hoisting.

type TracerConfig = {
  headers: (keyof BodyToHeaderParse<ContentMeta>)[];
};

const config: TracerConfig = {
  headers: [
    'x-at-service-id',
    'x-at-service-env',
    'x-at-site-version',
    'x-at-time',
    'x-at-device-type',
    'x-at-location-info-page-type',
    'x-at-location-info-section-id',
    'x-at-location-info-section-name',
    'x-at-location-info-page-id',
    'x-at-site-version-identifier',
    'x-at-paywall',
    'x-at-access-model'
  ]
};

tracer.use('fastify', config);

export default tracer;
