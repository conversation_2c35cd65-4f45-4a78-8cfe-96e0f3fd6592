import { HttpException, Injectable } from '@nestjs/common';
import {
  AdConfigDeviceTypeEnum,
  AdConfigOverride,
  CustomHttpStatus,
  LogLevel,
  PlaceholdersDetails,
  returnAsArrayEmpty
} from 'ads-layouts-tools';
import * as dayjs from 'dayjs';
import { normalizeResponseVersionField, removePrioParams, returnAsArray } from 'Helpers';
import {
  CommonRequest,
  IDebugData,
  IDebugOptions,
  IEngineResult,
  IGeneratorResponse,
  IMatchedPlaceholdersToEvents,
  IMatchedPlaceholdersWithAdConfigs,
  IMergeResult,
  IRulesPriorities,
  MergePlaceholdersWithAdConfigsBaseOnEventsResult,
  PlaceholderType,
  RuleStats
} from 'InterfacesAndTypes';
import { CreateException, log } from 'Utils';
import { AdConfigService } from '../adConfigs/adConfig.service';
import { DisplayConfigService } from '../displayConfig/displayConfig.service';
import { ENV } from '../envalidConfig';
import { EventsService } from '../events/events.service';
import { RuleService } from '../rules/rules.service';
import { RulesEngine } from '../rulesEngine/rulesEngine';
import { ServiceToPackageMapService } from '../serviceToPackageMap/serviceToPackageMap.service';

@Injectable()
export class GeneratorService extends RulesEngine {
  constructor(
    public readonly ruleService: RuleService,
    public readonly adConfigService: AdConfigService,
    public readonly displayConfigService: DisplayConfigService,
    public readonly serviceToPackageMapService: ServiceToPackageMapService,
    public readonly eventService: EventsService
  ) {
    super(ruleService, serviceToPackageMapService);
  }

  async createResponse(
    content: CommonRequest,
    debugMode: boolean,
    omitCache = false
  ): Promise<IGeneratorResponse> {
    const appVersion = process.env.npm_package_version || 'Version not found';

    if (omitCache) {
      // update variant weights if cache is omitted to have the most recent data with no cache
      this.eventService.updateVariantWeights(true);
    }

    const {
      meta: {
        deviceType,
        serviceId,
        serviceEnv,
        siteVersion,
        locationInfoPageType,
        locationInfoSectionId,
        locationInfoPageId,
        time
      }
    } = content;

    const releaseVersion = await this.getReleaseVersion(
      serviceId,
      time,
      serviceEnv,
      siteVersion,
      omitCache
    );

    const selectedConfig = await this.adConfigService.selectPlaceholdersConfig(
      serviceId,
      deviceType,
      releaseVersion,
      locationInfoPageType,
      parseInt(time),
      locationInfoSectionId,
      locationInfoPageId,
      omitCache
    );

    const { allFactsNames, rulesPackage } = await this.engineSetup(content, omitCache);

    const engineRunResult = await this.getRulesEngineRunResult(content);

    const { successfulPlaceholdersMerge, failedPlaceholdersMerge, rulesStats } =
      await this.mergePlaceholdersWithAdConfigsBaseOnEvents(
        engineRunResult,
        allFactsNames,
        selectedConfig.placeholders,
        deviceType
      );

    const placeholdersWithHandledPrios = this.handleRulesPriorities(
      successfulPlaceholdersMerge
    );

    const debugData = this.handleRequestProcessingStats(
      placeholdersWithHandledPrios,
      failedPlaceholdersMerge,
      content,
      releaseVersion,
      selectedConfig.configName,
      selectedConfig.placeholders,
      rulesStats
    );

    return {
      debugData: debugMode ? debugData : undefined,
      requestMeta: content.meta,
      ...selectedConfig.commonConfigFields,
      placeholders: placeholdersWithHandledPrios,
      version: normalizeResponseVersionField(releaseVersion),
      adsConfigIdentificationData: {
        configName: selectedConfig.configName,
        serviceId: selectedConfig.serviceIdFromConfig,
        modifiedDate: dayjs(selectedConfig.modifiedDate).format('YYYY-MM-DD HH:mm:ss'),
        rulesPackage: rulesPackage || 'original rules (no rules package assigned)',
        releaseVersion,
        appVersion
      }
    };
  }

  private getReleaseVersion = async (
    serviceId: string,
    time: string,
    serviceEnv?: string,
    siteVersion?: string,
    omitCache = false
  ) => {
    const releaseVersion = await this.displayConfigService.selectReleaseVersion(
      {
        serviceId,
        serviceEnv,
        siteVersion,
        time
      },
      omitCache
    );

    const releaseLog = {
      releaseVersion,
      serviceId,
      serviceEnv,
      siteVersion
    };

    log('SELECTED_RELEASE', releaseLog, LogLevel.dev);

    if (!releaseVersion) {
      const errMsg = `WARN_${CustomHttpStatus.CANNOT_FIND_ANY_RELEASE}_CANNOT_FIND_ANY_RELEASE`;
      log(errMsg, releaseLog, LogLevel.warn);

      throw CreateException({
        message: `Cannot match any release to given serviceId: ${serviceId}, serviceEnv: ${serviceEnv}, siteVersion: ${siteVersion}. This problem in most cases is related to non-existing siteVersion. Make sure siteVersion "${siteVersion}" exist in config ${ENV.DISPLAY_CONFIG_URL}`,
        statusCode: CustomHttpStatus.CANNOT_FIND_ANY_RELEASE
      });
    }

    return releaseVersion;
  };

  private getRulesEngineRunResult = async (content: CommonRequest): Promise<IEngineResult> => {
    try {
      const result = (await this.engine.run(content)) as IEngineResult;

      log(
        'ENGINE_RULES_STATUS',
        {
          rules: result.almanac.ruleResults.map(el => ({
            name: el.name,
            conditionResult: el.result
          }))
        },
        LogLevel.dev
      );
      const successfulEvents = result.almanac.events.success;

      if (!successfulEvents.length) {
        log(
          `WARN_${CustomHttpStatus.LACK_OF_SUCCESSFUL_RULES}_LACK_OF_SUCCESSFUL_RULES`,
          {},
          LogLevel.warn
        );

        throw CreateException(
          {
            message: `Lack of successful rules (check rules conditions)`,
            statusCode: CustomHttpStatus.LACK_OF_SUCCESSFUL_RULES
          },
          undefined,
          '_getRulesEngineRunResult'
        );
      }

      return result;
    } catch (err) {
      if (err instanceof HttpException) {
        throw err;
      }

      log('ERROR_ENGINE_RUN_RESULT', { err }, LogLevel.error);

      throw CreateException({
        message: `Unknown Json Rules Engine result error. ${err}`,
        statusCode: CustomHttpStatus.ENGINE_RUN
      });
    }
  };

  private sortSuccessfulPlaceholders = (
    foundPlaceholdersAndEvents: IMatchedPlaceholdersToEvents[]
  ) => {
    const eachPlaceholderWithEventDataFlat = foundPlaceholdersAndEvents.flatMap(el => {
      const eventData = {
        adConfigGroup: el.successfulEvent.params.adConfigGroup,
        adConfigOverride: el.successfulEvent.params.adConfigOverride,
        eventPriority: el.successfulEvent.params.priority,
        priorityGroup: el.successfulEvent.params.priorityGroup,
        ruleName: el.ruleName,
        rulePriority: el.rulePriority
      };

      return returnAsArray(el.siteMapMatchedPlaceholders).map(placeholder => ({
        ...eventData,
        placeholder
      }));
    });

    const uniqAdConfigGroups: string[] = [
      ...new Set(eachPlaceholderWithEventDataFlat.map(el => el.adConfigGroup))
    ];

    const sortedPlaceholdersPerGroupById = uniqAdConfigGroups
      .map(groupName => {
        return eachPlaceholderWithEventDataFlat
          .filter(item => item.adConfigGroup === groupName)
          .sort((a, b) => {
            if (a.placeholder && b.placeholder) {
              if (+a.placeholder.id === +b.placeholder.id) {
                return a.rulePriority - b.rulePriority;
              }
              return +a.placeholder.id - +b.placeholder.id;
            }

            if (a.placeholder) return -1;
            if (b.placeholder) return 1;
            return 0;
          });
        // .sort(
        //   (a, b) => +a.placeholder.id - +b.placeholder.id || a.rulePriority - b.rulePriority
        // );
      })
      .flat();

    return sortedPlaceholdersPerGroupById;
  };

  private mergePlaceholdersWithAdConfigsBaseOnEvents = async (
    engineRunResult: IEngineResult,
    allFactsNames: string[],
    adsConfigsParam: PlaceholdersDetails[],
    deviceType: AdConfigDeviceTypeEnum
  ): Promise<MergePlaceholdersWithAdConfigsBaseOnEventsResult> => {
    const successfulPlaceholdersMerge: IMatchedPlaceholdersWithAdConfigs[] = [];
    const failedPlaceholdersMerge: PlaceholderType[] = [];

    let adsConfigs = adsConfigsParam;
    const allRulesStatus = engineRunResult.almanac.ruleResults;
    const successfulRules = allRulesStatus.filter(el => el.result);

    let rulesStats: RuleStats[] = allRulesStatus.map(el => ({
      ruleName: el.name,
      conditionPass: el.result
    }));

    const foundPlaceholdersAndEvents: IMatchedPlaceholdersToEvents[] = [];

    for (const successfulRule of successfulRules) {
      const ruleName = successfulRule.name;
      const successfulEvent = successfulRule.event!;

      const siteMapMatchedPlaceholders =
        await this.eventService.getSiteMapPlaceholdersBasedOnEvents(
          successfulEvent,
          engineRunResult.almanac,
          allFactsNames,
          deviceType
        );

      rulesStats = rulesStats.map(el => {
        if (el.ruleName === ruleName) {
          return {
            ...el,
            eventPass: returnAsArray(siteMapMatchedPlaceholders).some(el => !!el),
            selectedPlaceholders: siteMapMatchedPlaceholders
          };
        }
        return el;
      });

      log('SITEMAP_EVENTS_MATCHED_PLACEHOLDERS', { siteMapMatchedPlaceholders }, LogLevel.dev);

      foundPlaceholdersAndEvents.push({
        successfulEvent,
        siteMapMatchedPlaceholders,
        ruleName,
        rulePriority: successfulRule.priority!
      });
    }

    const sortedPlaceholders = this.sortSuccessfulPlaceholders(foundPlaceholdersAndEvents);

    for (const {
      adConfigGroup,
      adConfigOverride,
      eventPriority,
      priorityGroup,
      placeholder,
      ruleName,
      rulePriority
    } of sortedPlaceholders) {
      const mergeResult = this.mergeSiteMapPlaceholdersWithAdConfigs(
        adConfigGroup,
        adConfigOverride,
        eventPriority,
        rulePriority,
        priorityGroup,
        placeholder,
        adsConfigs
      );

      if (mergeResult.isSuccessfulMerge) {
        successfulPlaceholdersMerge.push(...mergeResult.mergeContent);

        rulesStats = rulesStats.map(el => {
          if (el.ruleName === ruleName) {
            return {
              ...el,
              mergePass: mergeResult.mergeContent?.length > 0,
              mergeResult: mergeResult.mergeContent.map(
                ({ id, type, AD_Config_group, AD_Config_element_id }) => ({
                  id,
                  type,
                  AD_Config_group,
                  AD_Config_element_id
                })
              )
            };
          }
          return el;
        });

        adsConfigs = this.removeUsedAdConfigs(adsConfigs, successfulPlaceholdersMerge);
      }

      if (
        mergeResult.isSuccessfulMerge === false &&
        mergeResult.unmergedPlaceholders?.placeholders
      ) {
        rulesStats = rulesStats.map(el => {
          if (el.ruleName === ruleName) {
            return {
              ...el,
              mergePass: false,
              mergeResult: mergeResult.unmergedPlaceholders
            };
          }
          return el;
        });

        failedPlaceholdersMerge.push(mergeResult.unmergedPlaceholders.placeholders);
      }
    }

    return { successfulPlaceholdersMerge, failedPlaceholdersMerge, rulesStats };
  };

  private handleRequestProcessingStats = (
    placeholdersWithHandledPrios: IRulesPriorities[],
    failedPlaceholdersMerge: PlaceholderType[],
    content: CommonRequest,
    releaseVersion: string,
    configName: string,
    adsConfigs: PlaceholdersDetails[],
    rulesStats: RuleStats[]
  ): IDebugData => {
    if (placeholdersWithHandledPrios.length === 0 && failedPlaceholdersMerge.length > 0) {
      const errMsg = `WARN_${CustomHttpStatus.CANNOT_MERGE_ANY_ADS_CONFIGS}_CANNOT_MERGE_ANY_ADS_CONFIGS_INTO_PLACEHOLDERS`;
      log(errMsg, { failedPlaceholdersMerge }, LogLevel.warn);
      throw CreateException({
        message: `Cannot merge any ads configs with placeholders`,
        statusCode: CustomHttpStatus.CANNOT_MERGE_ANY_ADS_CONFIGS
      });
    }

    const allRulesCount = rulesStats.length;

    const { successConditionsCount, successEventsCount, successMergeCount } =
      rulesStats.reduce(
        (acc, curr) => {
          if (curr.conditionPass) acc.successConditionsCount++;
          if (curr.eventPass) acc.successEventsCount++;
          if (curr.mergePass) acc.successMergeCount++;
          return acc;
        },
        { successConditionsCount: 0, successEventsCount: 0, successMergeCount: 0 }
      );

    const logMsg = `RESPONSE_STATS_OK_${successMergeCount}_FAIL_${allRulesCount - successMergeCount}_${
      content.meta.serviceId
    }_${content.meta.deviceType}_${content.type}_${
      content.meta.locationInfoSectionId
    }_${releaseVersion.replace('/', '').replaceAll('.', '_')}`;

    const rulesStatsFormatted = rulesStats.reduce(
      (acc: { success: RuleStats[]; fail: RuleStats[] }, el) => {
        acc[el.mergePass ? 'success' : 'fail'].push(el);

        return acc;
      },
      { success: [], fail: [] }
    );

    const debugData: IDebugData = {
      releaseVersion,
      allAvailableAdConfigGroups: adsConfigs.map(
        ac => `group: ${ac.AD_Config_group}, id: ${ac.AD_Config_element_id}`
      ),
      allRulesCount,
      successConditionsCount,
      successEventsCount,
      successMergeCount,
      shortSuccessMergeStats: placeholdersWithHandledPrios.map(p => ({
        placeholderId: p.id,
        group: p.AD_Config_group,
        groupId: p.AD_Config_element_id
      })),
      rulesStats: rulesStatsFormatted,
      reqBodyType: content.type,
      fullConfigName: configName
    };

    if (failedPlaceholdersMerge.length > 0) {
      log(`WARN_${logMsg}`, { debugData }, LogLevel.warn);
    } else {
      log(`SUCCESS_${logMsg}`, { debugData });
    }

    return debugData;
  };

  private removeUsedAdConfigs = (
    adsConfigs: PlaceholdersDetails[],
    successfulPlaceholdersMerge: IMatchedPlaceholdersWithAdConfigs[]
  ): PlaceholdersDetails[] =>
    adsConfigs.filter(ac => {
      const shouldKeep = !successfulPlaceholdersMerge.some(
        fp =>
          fp.AD_Config_group === ac.AD_Config_group &&
          fp.AD_Config_element_id === ac.AD_Config_element_id
      );
      if (!shouldKeep) {
        log(
          'REMOVE_USED_ADCONFIG',
          {
            AD_Config_group: ac.AD_Config_group,
            AD_Config_element_id: ac.AD_Config_element_id
          },
          LogLevel.dev
        );
      }
      return shouldKeep;
    });

  private handleRulesPriorities2 = (
    successfulPlaceholdersMerge: IMatchedPlaceholdersWithAdConfigs[]
  ): IRulesPriorities[] => {
    const { withoutGroup, withGroup } = successfulPlaceholdersMerge.reduce(
      (acc, curr) => {
        if (curr.priorityGroup) {
          acc.withGroup.push(curr);
        } else {
          acc.withoutGroup.push(curr);
        }
        return acc;
      },
      {
        withoutGroup: [] as IMatchedPlaceholdersWithAdConfigs[],
        withGroup: [] as IMatchedPlaceholdersWithAdConfigs[]
      }
    );

    const priorityGroupMap = withGroup.reduce((acc, placeholder) => {
      const group = placeholder.priorityGroup!;

      if (!acc.has(group)) {
        acc.set(group, []);
      }

      acc.get(group)!.push(placeholder);

      return acc;
    }, new Map<string, IMatchedPlaceholdersWithAdConfigs[]>());

    return removePrioParams([
      ...Array.from(priorityGroupMap.values()).flatMap(this.sortByIdAndPriority),
      ...withoutGroup
    ]);
  };

  private handleRulesPriorities = (
    successfulPlaceholdersMerge: IMatchedPlaceholdersWithAdConfigs[]
  ): IRulesPriorities[] => {
    const hasPriorityGroup = successfulPlaceholdersMerge.some(fp => fp.priorityGroup);

    if (hasPriorityGroup) {
      const uniqPriorityGroups = [
        ...new Set(successfulPlaceholdersMerge.map(fp => fp.priorityGroup))
      ];

      const placeholdersWithoutPriorityGroup = successfulPlaceholdersMerge.filter(
        fp => !fp.priorityGroup
      );

      const resultPlaceholders = uniqPriorityGroups.map(priorityGroup => {
        return successfulPlaceholdersMerge
          .filter(fp => fp.priorityGroup === priorityGroup)
          .sort(this.prioritySort)[0];
      });

      resultPlaceholders.push(...placeholdersWithoutPriorityGroup);

      return removePrioParams(resultPlaceholders);
    }

    return removePrioParams(this.sortByIdAndPriority(successfulPlaceholdersMerge));
  };

  private sortByIdAndPriority = (
    successfulPlaceholdersMerge: IMatchedPlaceholdersWithAdConfigs[]
  ): IMatchedPlaceholdersWithAdConfigs[] => {
    const priorityMap = successfulPlaceholdersMerge.reduce((acc, curr) => {
      const key = curr.id;

      if (!acc.has(key)) {
        acc.set(key, []);
      }

      acc.get(key)!.push(curr);

      return acc;
    }, new Map<string, IMatchedPlaceholdersWithAdConfigs[]>());

    const sortedPlaceholders = Array.from(priorityMap.values()).map(group => {
      if (group.length === 1) {
        return group[0];
      }

      return group.sort(this.prioritySort)[0];
    });

    return sortedPlaceholders;
  };

  prioritySort<
    T extends Pick<IMatchedPlaceholdersWithAdConfigs, 'rulePriority' | 'eventPriority'>
  >(a: T, b: T): number {
    if (a.rulePriority !== b.rulePriority) {
      return a.rulePriority - b.rulePriority;
    }
    return (a.eventPriority ?? 0) - (b.eventPriority ?? 0);
  }

  private mergeSiteMapPlaceholdersWithAdConfigs = (
    adConfigGroup: string,
    adConfigOverride: AdConfigOverride | undefined,
    eventPriority: number | undefined,
    rulePriority: number,
    priorityGroup: string | undefined,
    siteMapMatchedPlaceholders: PlaceholderType,
    adsConfigs: PlaceholdersDetails[]
  ): IMergeResult => {
    let eventAdConfigGroup = '';

    if (adConfigGroup) {
      eventAdConfigGroup = adConfigGroup;

      const availableAdConfigs = adsConfigs?.filter(
        pc => pc.AD_Config_group === eventAdConfigGroup
      );

      if (availableAdConfigs?.length) {
        return {
          isSuccessfulMerge: true,
          mergeContent: this.matchPlaceholdersWithAdConfigs(
            siteMapMatchedPlaceholders,
            availableAdConfigs,
            adConfigOverride,
            eventPriority || 1,
            rulePriority,
            priorityGroup
          )
        };
      }
    }
    return {
      isSuccessfulMerge: false,
      unmergedPlaceholders: {
        placeholders: siteMapMatchedPlaceholders,
        eventAdConfigGroup
      }
    };
  };

  private matchPlaceholdersWithAdConfigs = (
    siteMapMatchedPlaceholders: PlaceholderType,
    availableAdConfigs: PlaceholdersDetails[],
    ruleAdConfigOverride: AdConfigOverride | undefined,
    eventPriority: number,
    rulePriority: number,
    priorityGroup: string | undefined
  ): IMatchedPlaceholdersWithAdConfigs[] => {
    const placeholders: IMatchedPlaceholdersWithAdConfigs[] = [];

    const placeholderArray = returnAsArrayEmpty(siteMapMatchedPlaceholders); // Either length 1 or 0

    if (placeholderArray.length === 0) {
      return [];
    }

    const sortConfigs = (a: PlaceholdersDetails, b: PlaceholdersDetails): number => {
      if (+a.AD_Config_element_id < +b.AD_Config_element_id) return -1;
      if (+a.AD_Config_element_id > +b.AD_Config_element_id) return 1;
      return 0;
    };

    const sortedAdConfigs = availableAdConfigs.sort((a, b) => sortConfigs(a, b));

    for (let i = 0; i < placeholderArray.length && i < sortedAdConfigs?.length; i++) {
      const { id, width, height, ...otherSortedAdConfigs } = sortedAdConfigs[i];

      if (width === null || height === null) {
        log(
          'ERROR_CANNOT_MERGE_ADCONFIG_WITH_NULL_HEIGHT_OR_WIDTH',
          {
            adConfig: sortedAdConfigs[i],
            placeholder: siteMapMatchedPlaceholders
          },
          LogLevel.error
        );
        continue;
      }

      placeholders.push({
        ...siteMapMatchedPlaceholders,
        configId: id,
        ...otherSortedAdConfigs,
        width,
        height,
        ...ruleAdConfigOverride,
        eventPriority,
        rulePriority,
        priorityGroup
      });
    }

    return placeholders;
  };

  public retrieveDebugQueryParamOptions = (
    debugQueryParam: string | undefined
  ): IDebugOptions => {
    const debugIngredients = debugQueryParam?.split(',') || [];

    return {
      debug: debugIngredients[0] === 'true',
      omitCache: debugIngredients[1] === 'omitCache'
    };
  };
}

// I want to refactor the handleRulesPriorities method.
// Main idea is to sort the placeholders.
// If priorityGroup exists, then sort by priorityGroup, then by rulePriority, then by eventPriority.
// Also if priorityGroup exists, do not sort the placeholders without priorityGroup.
// If priorityGroup does not exist, then sort by id, then by rulePriority, then by eventPriority.
// I tried to refactor it myself, and the code became redundant.
// I want to reduce the amount of code, because it's hard to read and does not seem to be the best way to do it.
// Can you help me refactor it?
