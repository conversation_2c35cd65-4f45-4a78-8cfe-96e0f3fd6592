import { PlaceholderPositionEnum, Prettify } from 'ads-layouts-tools';

type NonPlaceholderIndex = { nonPlaceholderIndex: number };

export type FactBase = {
  type: string;
};

export type FactType = Prettify<
  FactBase & {
    id: string;
    meta?: { height: number; variant?: string };
  } & Partial<NonPlaceholderIndex>
>;

export type FactTypeWithElements = Prettify<FactType & { elements: FactTypeWithElements[] }>;

export type PlaceholderType = Pick<FactType, 'id' | 'type'>;

export enum PlaceholderEnum {
  PLACEHOLDER = 'placeholder',
  COLUMN_PLACEHOLDER = 'column-placeholder'
}

export type PosFactType = Prettify<FactType & NonPlaceholderIndex>;

type activityState = { isActive: boolean };

export type PlaceholderTypeActive = Prettify<PlaceholderType & activityState>;

type NeighboringPlaceholders = Record<PlaceholderPositionEnum, PlaceholderTypeActive | null>;

export type FactWithNeighbors = Prettify<
  PosFactType & activityState & NeighboringPlaceholders
>;
