{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2022", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "noImplicitAny": true, "strict": true, "useUnknownInCatchVariables": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "esModuleInterop": false, "paths": {"InterfacesAndTypes": ["src/interfacesAndTypes"], "Utils/*": ["src/utils/*"], "Utils": ["src/utils"], "LocalCache/*": ["src/localCache/*"], "Helpers": ["src/helpers"], "Mocks": ["test/mocks"], "TestUtils": ["test/utils"], "src/*": ["src/*"]}}, "include": ["test"], "files": ["src/main.ts"]}