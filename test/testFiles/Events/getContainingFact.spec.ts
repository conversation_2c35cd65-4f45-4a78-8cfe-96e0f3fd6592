import { createMock } from '@golevelup/ts-jest';
import { Test, TestingModule } from '@nestjs/testing';
import { IAlmanac } from 'InterfacesAndTypes';
import { CacheModule } from 'src/cacheModule/cache.module';
import { EventsService } from 'src/events/events.service';
import { VariantWeightsConfigService } from 'src/variantWeightsConfig/variantWeightsConfig.service';

jest.mock('Utils/logger');

describe('getContainingFact test suite', () => {
  let service: EventsService;
  let almanac: IAlmanac;

  beforeAll(async () => {
    const app: TestingModule = await Test.createTestingModule({
      imports: [CacheModule.register({ isActive: false })],
      providers: [
        EventsService,
        {
          provide: VariantWeightsConfigService,
          useValue: createMock()
        }
      ]
    }).compile();

    service = app.get(EventsService);

    const output = [{ type: 'test1' }];
    const mockedMap = new Map([['test1', output]]);

    almanac = {
      factValue: (name: string) => {
        return mockedMap.get(name);
      }
    } as unknown as IAlmanac;
  });

  describe('getContainingFact - Basic Functionality', () => {
    it('should be defined', async () => {
      expect(service['getContainingFact']).toBeDefined();
    });

    it('should return undefined when placeholder element does not match fact name', async () => {
      const output = [{ type: 'test1', elements: [] }];

      const almanacFactsNames: string[] = ['test1', 'test2', 'test3'];
      const placeholdersElements: string[] = ['test2'];
      const almanac = {
        factValue: () => output
      } as unknown as IAlmanac;

      const result = await service['getContainingFact'](
        almanacFactsNames,
        placeholdersElements,
        almanac
      );

      expect(result).toBeUndefined();
    });

    it('should return fact when placeholder element matches fact name', async () => {
      const output = [{ type: 'test1', elements: [] }];

      const almanacFactsNames: string[] = ['test1', 'test2', 'test3'];
      const placeholdersElements: string[] = ['test1'];
      const almanac = {
        factValue: () => output
      } as unknown as IAlmanac;

      const result = await service['getContainingFact'](
        almanacFactsNames,
        placeholdersElements,
        almanac
      );

      expect(result).toEqual(output);
    });
  });

  describe('getContainingFact - Complex Scenarios', () => {
    it('should handle multiple facts with elements containing placeholders', async () => {
      const fact1 = [
        {
          type: 'teaser-grid-module',
          elements: [
            { type: 'article', id: 'art1' },
            { type: 'column-placeholder', id: 'placeholder1' }
          ]
        }
      ];
      const fact2 = [
        {
          type: 'global-news',
          elements: [
            { type: 'paragraph', id: 'para1' },
            { type: 'photo', id: 'photo1' }
          ]
        }
      ];

      const mockedMap = new Map([
        ['teaser-grid-module', fact1],
        ['global-news', fact2]
      ]);

      const almanac = {
        factValue: (name: string) => mockedMap.get(name)
      } as unknown as IAlmanac;

      const result = await service['getContainingFact'](
        ['teaser-grid-module', 'global-news'],
        ['teaser-grid-module'],
        almanac
      );

      expect(result).toEqual(fact1);
    });

    it('should handle nested elements structure', async () => {
      const factWithNestedElements = [
        {
          type: 'complex-module',
          elements: [
            {
              type: 'container',
              elements: [
                { type: 'nested-placeholder', id: 'nested1' },
                { type: 'content', id: 'content1' }
              ]
            }
          ]
        }
      ];

      const almanac = {
        factValue: () => factWithNestedElements
      } as unknown as IAlmanac;

      const result = await service['getContainingFact'](
        ['complex-module'],
        ['complex-module'],
        almanac
      );

      expect(result).toEqual(factWithNestedElements);
    });
  });

  describe('getContainingFact - Edge Cases', () => {
    it('should return undefined when no facts or placeholders provided', async () => {
      const almanacFactsNames: string[] = [];
      const placeholdersElements: string[] = [];
      const almanac = {} as IAlmanac;

      const result = await service['getContainingFact'](
        almanacFactsNames,
        placeholdersElements,
        almanac
      );

      expect(result).toBeUndefined();
    });

    it('should handle empty almanacFactsNames array', async () => {
      const result = await service['getContainingFact']([], ['placeholder'], almanac);

      expect(result).toBeUndefined();
    });

    it('should handle empty placeholdersElements array', async () => {
      const result = await service['getContainingFact'](['test-fact'], [], almanac);

      expect(result).toBeUndefined();
    });
  });
});
