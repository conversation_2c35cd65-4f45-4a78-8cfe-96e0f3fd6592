import { HttpStatus } from '@nestjs/common';
import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import {
  AdConfig,
  AdConfigDeviceTypeEnum,
  AdConfigSchema,
  CachePartsEnum,
  CustomHttpStatus,
  ExtensionConfig,
  ExtensionConfigSchema,
  PlaceholdersDetails
} from 'ads-layouts-tools';
import {
  adConfigMock,
  expectedResult_1,
  expectedResult_2,
  expectedResult_3,
  expectedResult_4
} from 'Mocks';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { connect, Connection, Model } from 'mongoose';
import { AdConfigService } from 'src/adConfigs/adConfig.service';
import { CacheModule } from 'src/cacheModule/cache.module';
import { CacheService } from 'src/cacheModule/cache.service';
import { ExtensionService } from 'src/extensionConfig/extension.service';
import { CreateException } from 'Utils';

jest.mock('Utils/logger');

describe('AdConfig service test suite', () => {
  let mongod: MongoMemoryServer;
  let mongoConnection: Connection;
  let mongoAdConfigModel: Model<AdConfig>;
  let mongoExtensionConfigModel: Model<ExtensionConfig>;
  let adConfigService: AdConfigService;
  let extensionService: ExtensionService;
  let cacheService: CacheService;
  let cacheGetSpy: jest.SpyInstance;
  let cacheSetSpy: jest.SpyInstance;
  let cacheDeleteByPrefixSpy: jest.SpyInstance;
  let mongoAdConfigModelSpy: jest.SpyInstance;
  let checkExtensionStatusSpy: jest.SpyInstance;
  let checkExtensionScheduleStatusSpy: jest.SpyInstance;

  beforeAll(async () => {
    mongod = await MongoMemoryServer.create();
    const uri = mongod.getUri();
    mongoConnection = (await connect(uri)).connection;

    mongoAdConfigModel = mongoConnection.model(AdConfig.name, AdConfigSchema);
    mongoExtensionConfigModel = mongoConnection.model(
      ExtensionConfig.name,
      ExtensionConfigSchema
    );

    const app: TestingModule = await Test.createTestingModule({
      imports: [CacheModule.register({ isActive: true })],
      providers: [
        AdConfigService,
        {
          provide: getModelToken(AdConfig.name),
          useValue: mongoAdConfigModel
        },
        ExtensionService,
        {
          provide: getModelToken(ExtensionConfig.name),
          useValue: mongoExtensionConfigModel
        }
      ]
    }).compile();

    adConfigService = app.get(AdConfigService);
    extensionService = app.get(ExtensionService);
    cacheService = app.get(CacheService);

    checkExtensionStatusSpy = jest.spyOn(extensionService, 'checkExtensionStatus');
    checkExtensionScheduleStatusSpy = jest.spyOn(
      extensionService,
      'checkExtensionScheduleStatus'
    );
  });

  beforeEach(async () => {
    await mongoAdConfigModel.insertMany(adConfigMock);
  });

  afterEach(async () => {
    await mongoAdConfigModel.deleteMany();
    jest.restoreAllMocks();
  });

  afterAll(async () => {
    await mongoConnection.dropDatabase();
    await mongoConnection.close();
    await mongod.stop();
  });

  describe('AdConfigService', () => {
    describe('Cache reset', () => {
      it('should return correctly reset cache on demand', async () => {
        //Setup
        const serviceId = 'ddtvn';
        const deviceType = AdConfigDeviceTypeEnum.DESKTOP;
        const release = 'release/1.52.0/';
        const pageType = 'main_page';
        const metaTime = 1723647600000;
        const sectionId = '111';
        const pageId = '7537055';

        mongoAdConfigModelSpy = jest.spyOn(mongoAdConfigModel, 'find');
        cacheGetSpy = jest.spyOn(cacheService, 'get');
        cacheSetSpy = jest.spyOn(cacheService, 'set');
        cacheDeleteByPrefixSpy = jest.spyOn(cacheService, 'deleteByPrefix');
        checkExtensionStatusSpy.mockResolvedValue(false);
        checkExtensionScheduleStatusSpy.mockResolvedValue(true);

        type CacheCallsType = Record<
          keyof Pick<CacheService, 'get' | 'set' | 'deleteByPrefix'>,
          number
        >;
        type DbCallsType = Record<keyof Pick<Model<AdConfig>, 'find'>, number>;

        const cacheCalls: CacheCallsType = { get: 0, set: 0, deleteByPrefix: 0 };
        const dbCalls: DbCallsType = { find: 0 };

        const callAndCheck = async (
          { get = 0, set = 0, deleteByPrefix = 0 }: Partial<CacheCallsType>,
          { find = 0 }: Partial<DbCallsType> = {}
        ) => {
          cacheCalls.get += get;
          cacheCalls.set += set;
          cacheCalls.deleteByPrefix += deleteByPrefix;
          dbCalls.find += find;

          const selectedConfig = await adConfigService.selectPlaceholdersConfig(
            serviceId,
            deviceType,
            release,
            pageType,
            metaTime,
            sectionId,
            pageId
          );

          expect(selectedConfig).toMatchObject(expectedResult_1);

          expect(cacheGetSpy).toHaveBeenCalledTimes(cacheCalls.get);
          expect(cacheSetSpy).toHaveBeenCalledTimes(cacheCalls.set);
          expect(cacheDeleteByPrefixSpy).toHaveBeenCalledTimes(cacheCalls.deleteByPrefix);
          expect(mongoAdConfigModelSpy).toHaveBeenCalledTimes(dbCalls.find);
        };

        //Testing

        // miss cache, get configs from DB and set configs to cache
        await callAndCheck({ get: 1, set: 1 }, { find: 1 });

        // get configs from cache
        await callAndCheck({ get: 1 });

        // delete configs from cache
        const numberOfDeletedKeys = await cacheService.deleteByPrefix(
          `${CachePartsEnum.AD_CONFIGS}__${serviceId}`
        );
        expect(numberOfDeletedKeys).toEqual(1); // optional check
        cacheCalls.deleteByPrefix += 1; // manually increment cacheCalls

        // miss cache, get configs from DB and set configs to cache
        await callAndCheck({ get: 1, set: 1 }, { find: 1 });

        // get configs from cache
        await callAndCheck({ get: 1 });

        jest.clearAllMocks();
      });
    });

    describe('getAllAdConfigs', () => {
      it('should return correct data length', async () => {
        const allConfigs = await adConfigService.getAllAdConfigs();
        expect(allConfigs.length).toEqual(adConfigMock.length);
      });

      it('should throw when no data was found', async () => {
        await mongoAdConfigModel.deleteMany({});
        void expect(adConfigService.getAllAdConfigs()).rejects.toThrow(
          CreateException({
            message: 'AdConfigs data not found!',
            statusCode: HttpStatus.NOT_FOUND
          })
        );
      });
    });

    describe('partialFilterAdConfigs', () => {
      const serviceId = '';
      const metaTime = 1723647600000;
      const deviceType = '' as AdConfigDeviceTypeEnum;
      const release = '';
      const pageType = '';
      const sectionId = '';
      const pageId = '';
      const mockAdConfig = {
        config: { placeholders: [] },
        adsLayoutsAdditionalData: { releaseName: '' }
      } as unknown as AdConfig;

      let filterAdConfigsBodyStub: jest.SpyInstance;
      beforeEach(() => {
        filterAdConfigsBodyStub = jest.spyOn(adConfigService, 'filterAdConfigsBody');
      });

      it('should throw when no data was found', async () => {
        await mongoAdConfigModel.deleteMany({});
        void expect(
          adConfigService.partialFilterAdConfigs(
            serviceId,
            metaTime,
            deviceType,
            release,
            pageType,
            sectionId,
            pageId
          )(mockAdConfig)
        ).rejects.toThrow(
          CreateException({
            message: `Unable to find any adConfigs for given params: serviceId: ${serviceId}, deviceType: ${deviceType}, release: ${release}, pageType: ${pageType}, sectionId: ${sectionId}, pageId: ${pageId}`,
            statusCode: HttpStatus.BAD_REQUEST
          })
        );
      });

      it('should return correct data', async () => {
        const mockAdConfig = {
          config: { placeholders: ['array length longer than zero'] },
          adsLayoutsAdditionalData: { releaseName: '' }
        } as unknown as AdConfig;

        filterAdConfigsBodyStub.mockResolvedValue(expectedResult_1);

        const result = await adConfigService.partialFilterAdConfigs(
          serviceId,
          metaTime,
          deviceType,
          release,
          pageType,
          sectionId,
          pageId
        )(mockAdConfig);
        expect(result).toEqual(expectedResult_1);
      });

      it('should throw when no data in config', async () => {
        void expect(
          adConfigService.partialFilterAdConfigs(
            serviceId,
            metaTime,
            deviceType,
            release,
            pageType,
            sectionId,
            pageId
          )(mockAdConfig)
        ).rejects.toThrow(
          CreateException({
            message: `Unable to find any adConfigs for given params: serviceId: ${serviceId}, deviceType: ${deviceType}, release: ${release}, pageType: ${pageType}, sectionId: ${sectionId}, pageId: ${pageId}`,
            statusCode: CustomHttpStatus.CANNOT_FIND_ADCONFIGS_FOR_PARAMS
          })
        );
      });
    });

    describe('filterEnabledPlaceholders', () => {
      it('should return correct data', () => {
        const result = adConfigService.filterEnabledPlaceholders(
          adConfigMock[0] as unknown as AdConfig,
          'SectionId not used'
        );
        expect(result).toMatchObject(adConfigMock[0].config.placeholders);
      });

      it('should throw when no data was found', () => {
        const sectionId = '123';
        expect(() =>
          adConfigService.filterEnabledPlaceholders(
            adConfigMock[3] as unknown as AdConfig,
            sectionId
          )
        ).toThrow(
          '3 adConfigs were found, but all of them are disabled. No enabled adConfigs matched the provided parameters - release: release/1.42.0/, pageType: report_report_descriptive,report_report_descriptive_mourning_full,report_report_descriptive_mourning_light, sectionId: 123, pageId: .'
        );
      });
    });

    describe('filterByDeviceType', () => {
      it('should return correct data', () => {
        const placeholders = adConfigMock[0].config.placeholders as PlaceholdersDetails[];
        const result = adConfigService.filterByDeviceType(
          placeholders,
          AdConfigDeviceTypeEnum.DESKTOP
        );
        expect(result).toMatchObject([adConfigMock[0].config.placeholders[0]]);
      });

      it('should throw when no data was found', () => {
        expect(() =>
          adConfigService.filterByDeviceType([], AdConfigDeviceTypeEnum.DESKTOP)
        ).toThrow('Cannot find any adConfigs for provided params: deviceType: desktop');
      });
    });

    describe('filterBaseOnExtensionConfig', () => {
      const metaTime = 1723647600000;

      beforeEach(() => {
        checkExtensionStatusSpy = jest.spyOn(extensionService, 'checkExtensionStatus');
        checkExtensionScheduleStatusSpy = jest.spyOn(
          extensionService,
          'checkExtensionScheduleStatus'
        );
      });

      it("should throw when cannot find any adConfigs for given extension when extensionList is empty and ids are not 'superpanel' or 'top_premium'", async () => {
        checkExtensionStatusSpy.mockResolvedValue(false);
        checkExtensionScheduleStatusSpy.mockResolvedValue(false);

        await expect(
          adConfigService.filterBaseOnExtensionConfig(
            [],
            'mockedServiceId',
            metaTime,
            AdConfigDeviceTypeEnum.DESKTOP
          )
        ).rejects.toThrow('Cannot find any adConfigs for superPanel or topPremium config');
      });

      it("should throw when extensionList is empty and ids are 'superpanel' or 'top_premium'", async () => {
        checkExtensionStatusSpy.mockResolvedValue(false);
        checkExtensionScheduleStatusSpy.mockResolvedValue(true);

        await expect(
          adConfigService.filterBaseOnExtensionConfig(
            [{ id: 'superpanel', deviceType: ['smartphone'] }] as PlaceholdersDetails[],
            'ddtvn',
            metaTime,
            AdConfigDeviceTypeEnum.DESKTOP
          )
        ).rejects.toThrow('Cannot find any adConfigs for superPanel or topPremium config');
      });

      it('should return correct data', async () => {
        checkExtensionStatusSpy.mockResolvedValue(false);
        checkExtensionScheduleStatusSpy.mockResolvedValue(true);

        const result = await adConfigService.filterBaseOnExtensionConfig(
          [adConfigMock[0].config.placeholders[0]] as PlaceholdersDetails[],
          'ddtvn',
          metaTime,
          AdConfigDeviceTypeEnum.DESKTOP
        );

        expect(result).toMatchObject([adConfigMock[0].config.placeholders[0]]);
      });
    });

    describe('filterAdConfigsBody', () => {
      const metaTime = 1723647600000;
      beforeEach(() => {
        jest.spyOn(extensionService, 'checkExtensionStatus').mockResolvedValue(false);
      });

      it('should throw when cannot find any adConfigs for provided deviceType', () => {
        void expect(async () => {
          await adConfigService.filterAdConfigsBody(
            adConfigMock[4],
            metaTime,
            '',
            AdConfigDeviceTypeEnum.TABLET
          );
        }).rejects.toThrow(
          'Cannot find any adConfigs for provided params: deviceType: tablet'
        );
      });

      it('should throw when cannot find any adConfigs for given extension when extensionList is empty', () => {
        void expect(async () => {
          await adConfigService.filterAdConfigsBody(
            adConfigMock[4],
            metaTime,
            '',
            AdConfigDeviceTypeEnum.SMARTPHONE
          );
        }).rejects.toThrow('Cannot find any adConfigs for superPanel or topPremium config');
      });

      it('should throw when cannot find any adConfigs for given extension when checkExtensionStatus is false', () => {
        void expect(async () => {
          await adConfigService.filterAdConfigsBody(
            adConfigMock[4],
            metaTime,
            'existingServiceId',
            AdConfigDeviceTypeEnum.SMARTPHONE
          );
        }).rejects.toThrow('Cannot find any adConfigs for superPanel or topPremium config');
      });

      it("should return correct data if id of filtered placeholders is not 'superpanel' or 'top_premium'", async () => {
        const result = await adConfigService.filterAdConfigsBody(
          adConfigMock[4],
          metaTime,
          'biznes',
          AdConfigDeviceTypeEnum.DESKTOP
        );
        expect(result).toMatchObject(expectedResult_4);
      });
    });

    describe('selectPlaceholdersConfig', () => {
      beforeEach(() => {
        jest.spyOn(extensionService, 'checkExtensionStatus').mockResolvedValue(false);
      });

      it('Function with params for no AdConfig should throw Exception', () => {
        const serviceId = 'ddtvn';
        const deviceType = AdConfigDeviceTypeEnum.DESKTOP;
        const release = 'release/1.2.3/';
        const pageType = 'story_story_video';
        const metaTime = 1723647600000;
        const sectionId = '222';
        const pageId = '7537055';

        void expect(async () => {
          await adConfigService.selectPlaceholdersConfig(
            serviceId,
            deviceType,
            release,
            pageType,
            metaTime,
            sectionId,
            pageId
          );
        }).rejects.toThrow('Cannot find any adConfigs for provided params');
      });

      it('should return correct data for correct params', async () => {
        const serviceId = 'ddtvn';
        const deviceType = AdConfigDeviceTypeEnum.DESKTOP;
        const release = 'release/1.52.0/';
        const pageType = 'main_page';
        const metaTime = 1723647600000;
        const sectionId = '111';
        const pageId = '7537055';

        const selectedConfig = await adConfigService.selectPlaceholdersConfig(
          serviceId,
          deviceType,
          release,
          pageType,
          metaTime,
          sectionId,
          pageId
        );

        expect(selectedConfig).toMatchObject(expectedResult_1);
      });

      it('should throw Error when there is no adConfigs in cache and no data in database', () => {
        const serviceId = 'ServiceIdNotPresentInDB';
        const deviceType = '' as AdConfigDeviceTypeEnum;
        const release = 'ReleaseNameNotPresentInDB';
        const pageType = 'PageTypeNotPresentInDB';
        const metaTime = 1723647600000;

        void expect(async () => {
          await adConfigService.selectPlaceholdersConfig(
            serviceId,
            deviceType,
            release,
            pageType,
            metaTime
          );
        }).rejects.toThrow(
          `Cannot find any adConfigs for provided params release: ${release}, pageType: ${pageType}` //, sectionId: ${sectionId}, pageId: ${pageId}`
        );
      });

      it('should throw when data from cache is empty', () => {
        const serviceId = 'ServiceIdNotPresentInDB';
        const deviceType = '' as AdConfigDeviceTypeEnum;
        const release = 'ReleaseNameNotPresentInDB';
        const pageType = 'PageTypeNotPresentInDB';
        const metaTime = 1723647600000;

        void expect(async () => {
          await adConfigService.selectPlaceholdersConfig(
            serviceId,
            deviceType,
            release,
            pageType,
            metaTime
          );
        }).rejects.toThrow(
          `Cannot find any adConfigs for provided params release: ${release}, pageType: ${pageType}` //, sectionId: ${sectionId}, pageId: ${undefined}`
        );
      });

      it('should return correct data when sectionId and pageId are defined', async () => {
        const serviceId = 'ddtvn';
        const deviceType = AdConfigDeviceTypeEnum.DESKTOP;
        const release = 'release/1.52.0/';
        const pageType = 'main_page';
        const metaTime = 1723647600000;
        const sectionId = '111';
        const pageId = '7537055';

        const selectedConfig = await adConfigService.selectPlaceholdersConfig(
          serviceId,
          deviceType,
          release,
          pageType,
          metaTime,
          sectionId,
          pageId
        );
        expect(selectedConfig).toMatchObject(expectedResult_1);
      });

      it('should return correct data when only sectionId is defined', async () => {
        const serviceId = 'biznes';
        const deviceType = AdConfigDeviceTypeEnum.TABLET;
        const release = 'release/1.44.0/';
        const pageType = 'column';
        const metaTime = 1723647600000;
        const sectionId = '222';
        const pageId = '';

        const selectedConfig = await adConfigService.selectPlaceholdersConfig(
          serviceId,
          deviceType,
          release,
          pageType,
          metaTime,
          sectionId,
          pageId
        );

        expect(selectedConfig).toMatchObject(expectedResult_2);
      });

      it('should return correct data when only pageId is defined', async () => {
        const serviceId = 'biznes';
        const deviceType = AdConfigDeviceTypeEnum.SMARTPHONE;
        const release = 'release/1.44.3/';
        const pageType = 'gallery';
        const metaTime = 1723647600000;
        const sectionId = '';
        const pageId = '1234';

        const selectedConfig = await adConfigService.selectPlaceholdersConfig(
          serviceId,
          deviceType,
          release,
          pageType,
          metaTime,
          sectionId,
          pageId
        );

        expect(selectedConfig).toMatchObject(expectedResult_3);
      });

      it('should return correct data when sectionId and pageId are not defined', async () => {
        const serviceId = 'biznes';
        const deviceType = AdConfigDeviceTypeEnum.SMARTPHONE;
        const release = 'release/1.44.3/';
        const pageType = 'gallery';
        const metaTime = 1723647600000;
        const sectionId = '';
        const pageId = '';

        const selectedConfig = await adConfigService.selectPlaceholdersConfig(
          serviceId,
          deviceType,
          release,
          pageType,
          metaTime,
          sectionId,
          pageId
        );

        expect(selectedConfig).toMatchObject(expectedResult_3);
      });

      it("should return 'onlyEmptySectionIdConfig' when sectionId and pageId are defined but data was not found", async () => {
        const serviceId = 'biznes';
        const deviceType = AdConfigDeviceTypeEnum.SMARTPHONE;
        const release = 'release/1.44.3/';
        const pageType = 'gallery';
        const metaTime = 1723647600000;
        const sectionId = 'nonExistingSectionId';
        const pageId = 'nonExistingPageId';

        const selectedConfig = await adConfigService.selectPlaceholdersConfig(
          serviceId,
          deviceType,
          release,
          pageType,
          metaTime,
          sectionId,
          pageId
        );

        expect(selectedConfig).toMatchObject(expectedResult_3);
      });
    });
  });
});
