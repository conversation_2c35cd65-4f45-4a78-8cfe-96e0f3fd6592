import { HttpException } from '@nestjs/common';
import { getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import {
  AdConfig,
  AdConfigDeviceTypeEnum,
  AdConfigSchema,
  AllConditions,
  AllConditionsSchema,
  AnyConditions,
  AnyConditionsSchema,
  Conditions,
  ConditionsSchema,
  CustomHttpStatus,
  DisplayConfig,
  DisplayConfigSchema,
  Event,
  EventSchema,
  ExtensionConfig,
  ExtensionConfigSchema,
  PlaceholdersDetails,
  Rule,
  RuleSchema,
  ServiceToPackageMap,
  ServiceToPackageMapSchema,
  VariantWeightsConfig,
  VariantWeightsConfigSchema
} from 'ads-layouts-tools';
import {
  CommonRequest,
  IGeneratorResponse,
  IRulesPriorities,
  PlaceholderType,
  RuleStats
} from 'InterfacesAndTypes';
import { cloneDeep } from 'lodash';
import {
  adConfigDB,
  body_1,
  body_2,
  body_3,
  body_3_AnyFact,
  body_4,
  conditionsDB,
  displayConfigDB,
  eventsDB,
  extensionConfigDummy,
  outputMock_AnyFact,
  outputMock_base,
  outputMock_DefaultPriorities,
  outputMock_ModifiedPriorities,
  outputMock_package,
  rulesDB,
  serviceToPackageMapDB,
  variantConfigDummy
} from 'Mocks';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { connect, Connection, Model } from 'mongoose';
import { AdConfigService } from 'src/adConfigs/adConfig.service';
import { CacheModule } from 'src/cacheModule/cache.module';
import { DisplayConfigService } from 'src/displayConfig/displayConfig.service';
import { EventsService } from 'src/events/events.service';
import { ExtensionService } from 'src/extensionConfig/extension.service';
import { GeneratorService } from 'src/generator/generator.service';
import { RuleService } from 'src/rules/rules.service';
import { ServiceToPackageMapService } from 'src/serviceToPackageMap/serviceToPackageMap.service';
import { CreateException } from 'src/utils';
import { VariantWeightsConfigService } from 'src/variantWeightsConfig/variantWeightsConfig.service';
import { PartialDeep } from 'type-fest';

type testParams2XXCodes = {
  params: PartialDeep<CommonRequest, { recurseIntoArrays: false }>;
  statusCode: CustomHttpStatus;
  message: string;
};

jest.mock('Utils/logger');

describe('Generator service test suite', () => {
  let mongod: MongoMemoryServer;
  let mongoConnection: Connection;
  let mongoRuleModel: Model<Rule>;
  let mongoConditionsModel: Model<Conditions>;
  let mongoEventsModel: Model<Event>;
  let mongoAdConfigModel: Model<AdConfig>;
  let mongoDisplayConfigModel: Model<DisplayConfig>;
  let mongoExtensionConfigModel: Model<ExtensionConfig>;
  let mongoServiceToPackageMapModel: Model<ServiceToPackageMap>;
  let mongoVariantWeightsConfigModel: Model<VariantWeightsConfig>;
  let service: GeneratorService;

  beforeAll(async () => {
    mongod = await MongoMemoryServer.create();
    const uri = mongod.getUri();
    mongoConnection = (await connect(uri)).connection;

    mongoConditionsModel = mongoConnection.model(Conditions.name, ConditionsSchema);
    mongoConditionsModel.discriminator(AnyConditions.name, AnyConditionsSchema);
    mongoConditionsModel.discriminator(AllConditions.name, AllConditionsSchema);
    mongoEventsModel = mongoConnection.model(Event.name, EventSchema);
    mongoRuleModel = mongoConnection.model(Rule.name, RuleSchema);
    mongoAdConfigModel = mongoConnection.model(AdConfig.name, AdConfigSchema);
    mongoDisplayConfigModel = mongoConnection.model(DisplayConfig.name, DisplayConfigSchema);
    mongoExtensionConfigModel = mongoConnection.model(
      ExtensionConfig.name,
      ExtensionConfigSchema
    );
    mongoServiceToPackageMapModel = mongoConnection.model(
      ServiceToPackageMap.name,
      ServiceToPackageMapSchema
    );
    mongoVariantWeightsConfigModel = mongoConnection.model(
      VariantWeightsConfig.name,
      VariantWeightsConfigSchema
    );

    const app: TestingModule = await Test.createTestingModule({
      imports: [CacheModule.register({ isActive: false })],
      providers: [
        GeneratorService,
        RuleService,
        AdConfigService,
        DisplayConfigService,
        ExtensionService,
        ServiceToPackageMapService,
        VariantWeightsConfigService,
        EventsService,
        {
          provide: getModelToken(Conditions.name),
          useValue: mongoConditionsModel
        },
        {
          provide: getModelToken(Event.name),
          useValue: mongoEventsModel
        },
        {
          provide: getModelToken(Rule.name),
          useValue: mongoRuleModel
        },
        {
          provide: getModelToken(AdConfig.name),
          useValue: mongoAdConfigModel
        },
        {
          provide: getModelToken(DisplayConfig.name),
          useValue: mongoDisplayConfigModel
        },
        {
          provide: getModelToken(ExtensionConfig.name),
          useValue: mongoExtensionConfigModel
        },
        {
          provide: getModelToken(ServiceToPackageMap.name),
          useValue: mongoServiceToPackageMapModel
        },
        {
          provide: getModelToken(VariantWeightsConfig.name),
          useValue: mongoVariantWeightsConfigModel
        }
      ]
    }).compile();

    service = app.get(GeneratorService);
  });

  beforeEach(async () => {
    await mongoConditionsModel.insertMany(conditionsDB);
    await mongoEventsModel.insertMany(eventsDB);
    await mongoRuleModel.insertMany(rulesDB);
    await mongoAdConfigModel.insertMany(adConfigDB);
    await mongoDisplayConfigModel.insertMany(displayConfigDB);
    await mongoExtensionConfigModel.insertMany(extensionConfigDummy);
    await mongoVariantWeightsConfigModel.insertMany(variantConfigDummy);
    await mongoServiceToPackageMapModel.insertMany(serviceToPackageMapDB);
  });

  afterEach(async () => {
    await mongoDisplayConfigModel.deleteMany({});
    await mongoAdConfigModel.deleteMany({});
    await mongoConditionsModel.deleteMany({});
    await mongoEventsModel.deleteMany({});
    await mongoRuleModel.deleteMany({});
    await mongoExtensionConfigModel.deleteMany({});
    await mongoVariantWeightsConfigModel.deleteMany({});
    await mongoServiceToPackageMapModel.deleteMany({});
  });

  afterAll(async () => {
    await mongoConnection.dropDatabase();
    await mongoConnection.close();
    await mongod.stop();
  });

  const prepareMockedBody = (
    params: PartialDeep<CommonRequest, { recurseIntoArrays: false }>
  ): CommonRequest => {
    const bodyCopy = cloneDeep(body_1);

    for (const [key, value] of Object.entries(params)) {
      if (key === 'elements') {
        bodyCopy['elements'] = value as CommonRequest['elements'];
      }
      Object.assign(bodyCopy[key as keyof CommonRequest], value);
    }

    return bodyCopy;
  };

  const expectCustomHttpException = async (input: testParams2XXCodes) => {
    const bodyCopy = prepareMockedBody(input.params);

    try {
      await service.createResponse(bodyCopy, true);
      throw new Error("Should've thrown HttpException");
    } catch (e: unknown) {
      if (e instanceof HttpException) {
        expect(e).toMatchObject({
          message: input.message,
          status: input.statusCode
        });
      } else {
        throw e;
      }
    }
  };

  const checkResponse = (response: IGeneratorResponse, mockedResponse: IGeneratorResponse) => {
    const { debugData, placeholders, adsConfigIdentificationData } = mockedResponse;

    expect(debugData).toBeDefined();

    const {
      shortSuccessMergeStats,
      rulesStats: { fail, success },
      allAvailableAdConfigGroups
    } = debugData!;

    expect(response).toStrictEqual({
      ...mockedResponse,
      debugData: expect.objectContaining({
        ...debugData,
        allAvailableAdConfigGroups: expect.arrayContaining(allAvailableAdConfigGroups),
        shortSuccessMergeStats: expect.arrayContaining(shortSuccessMergeStats),
        rulesStats: expect.objectContaining({
          success: expect.arrayContaining(success),
          fail: expect.arrayContaining(fail)
        })
      }),
      placeholders: expect.arrayContaining(placeholders),
      adsConfigIdentificationData: expect.objectContaining({
        ...adsConfigIdentificationData,
        modifiedDate: expect.any(String),
        appVersion: expect.any(String)
      })
    });

    // Check that arrays have the same length
    expect(response.debugData?.allAvailableAdConfigGroups.length).toEqual(
      allAvailableAdConfigGroups.length
    );
    expect(response.debugData?.shortSuccessMergeStats.length).toEqual(
      shortSuccessMergeStats.length
    );
    expect(response.debugData?.rulesStats.success.length).toEqual(success.length);
    expect(response.debugData?.rulesStats.fail.length).toEqual(fail.length);
    expect(response.placeholders.length).toEqual(placeholders.length);

    // Check that arrays have the same order
    for (let i = 0; i < allAvailableAdConfigGroups.length; i++) {
      expect(response.debugData?.allAvailableAdConfigGroups[i]).toEqual(
        allAvailableAdConfigGroups[i]
      );
    }

    for (let i = 0; i < shortSuccessMergeStats.length; i++) {
      expect(response.debugData?.shortSuccessMergeStats[i]).toMatchObject(
        shortSuccessMergeStats[i]
      );
    }

    for (let i = 0; i < success.length; i++) {
      expect(response.debugData?.rulesStats.success[i]).toMatchObject(success[i]);
    }

    for (let i = 0; i < fail.length; i++) {
      expect(response.debugData?.rulesStats.fail[i]).toMatchObject(fail[i]);
    }

    for (let i = 0; i < placeholders.length; i++) {
      expect(response.placeholders[i]).toMatchObject(placeholders[i]);
    }
  };

  describe('basic invocation - correct responses', () => {
    it('base case', async () => {
      const response = await service.createResponse(body_1, true);

      checkResponse(response, outputMock_base);
    });

    it('rules package included', async () => {
      const response = await service.createResponse(body_2, true);

      checkResponse(response, outputMock_package);
    });

    it("should return correct response - nearToIndexes event - fact accepts 'any' value", async () => {
      const response = await service.createResponse(body_3_AnyFact, true);

      checkResponse(response, outputMock_AnyFact);
    });
  });

  describe('Priorities', () => {
    it('Default Priorities', async () => {
      const response = await service.createResponse(body_3, true);

      checkResponse(response, outputMock_DefaultPriorities);
    });

    it('Mixed Priorities', async () => {
      const response = await service.createResponse(body_4, true);

      checkResponse(response, outputMock_ModifiedPriorities);
    });
  });

  describe('custom 2xx http status codes', () => {
    it('should throw: 210 - cannot find any rules', async () => {
      const input: testParams2XXCodes = {
        params: {
          meta: {
            deviceType: AdConfigDeviceTypeEnum.SMARTPHONE,
            serviceId: 'vod',
            locationInfoPageType: 'story_content_hub',
            locationInfoSectionId: '1',
            locationInfoPageId: '4598886'
          }
        },
        statusCode: CustomHttpStatus.CANNOT_FIND_ANY_RULES,
        message:
          'Cannot get any rules data for rulesPackage: original rules, type: article, serviceId: vod, deviceType: smartphone, sectionId: 1, pageId: 4598886, pageType: story_content_hub, siteVersion: ab_atsdk_ga, accessModel: undefined, paywall: undefined'
      };

      await expectCustomHttpException(input);
    });

    it('should throw: 211 - lack of successful rules', async () => {
      const input: testParams2XXCodes = {
        params: { elements: [] },
        statusCode: CustomHttpStatus.LACK_OF_SUCCESSFUL_RULES,
        message: 'Lack of successful rules (check rules conditions)'
      };

      await expectCustomHttpException(input);
    });

    it('should throw: 212 - cannot find any release - no DisplayConfigs', async () => {
      const input: testParams2XXCodes = {
        params: { meta: { serviceId: 'invalid', serviceEnv: 'invalid' } },
        statusCode: CustomHttpStatus.CANNOT_FIND_ANY_RELEASE,
        message: 'Lack of displayConfig for serviceId: invalid, env: invalid'
      };

      await expectCustomHttpException(input);
    });

    it('should throw: 212 - cannot find any release - cannot match by env, and default is not available', async () => {
      const input: testParams2XXCodes = {
        params: { meta: { serviceId: 'biznes' } },
        statusCode: CustomHttpStatus.CANNOT_FIND_ANY_RELEASE,
        message:
          'Cannot match any release to given serviceId: biznes, serviceEnv: production, siteVersion: ab_atsdk_ga. This problem in most cases is related to non-existing siteVersion. Make sure siteVersion \"ab_atsdk_ga\" exist in config https://display-at.cdntvn.pl/lambdaDisplayConfigs/config.json'
      };

      await expectCustomHttpException(input);
    });

    it('should throw: 213 - cannot find adConfigs for params - no adConfigs', async () => {
      const input: testParams2XXCodes = {
        params: { meta: { locationInfoPageType: 'invalid' } },
        statusCode: CustomHttpStatus.CANNOT_FIND_ADCONFIGS_FOR_PARAMS,
        message:
          'Cannot find any adConfigs for provided params release: release/1.63.0/, pageType: invalid, sectionId: 222, pageId: 7931689'
      };

      await expectCustomHttpException(input);
    });

    it('should throw: 213 - cannot find adConfigs for params - no placeholders in adConfigs', async () => {
      const input: testParams2XXCodes = {
        params: { meta: { locationInfoPageType: 'report_no_ads' } },
        statusCode: CustomHttpStatus.CANNOT_FIND_ADCONFIGS_FOR_PARAMS,
        message:
          'Unable to find any adConfigs for given params: serviceId: ddtvn, deviceType: desktop, release: release/1.63.0/, pageType: report_no_ads, sectionId: 222, pageId: 7931689'
      };

      await expectCustomHttpException(input);
    });

    it('should throw: 214 - cannot find adConfigs for deviceType', async () => {
      const input: testParams2XXCodes = {
        params: { meta: { deviceType: 'invalid' as any } },
        statusCode: CustomHttpStatus.CANNOT_FIND_ADCONFIGS_FOR_DEVICE,
        message: 'Cannot find any adConfigs for provided params: deviceType: invalid'
      };

      await expectCustomHttpException(input);
    });

    it('should throw: 215 - cannot find adConfigs for superPanel and topPremium', async () => {
      const input: testParams2XXCodes = {
        params: {
          meta: {
            serviceId: 'vod',
            locationInfoPageType: 'story_content_hub',
            locationInfoSectionId: '1',
            locationInfoPageId: '4598886'
          }
        },
        statusCode: CustomHttpStatus.CANNOT_FIND_ADCONFIGS_FOR_SUPERPANEL_AND_TOP_PREMIUM,
        message: 'Cannot find any adConfigs for superPanel or topPremium config'
      };

      await expectCustomHttpException(input);
    });

    it.skip('should throw: 216 - cannot merge any ads configs', async () => {
      const input: testParams2XXCodes = {
        params: {},
        statusCode: CustomHttpStatus.CANNOT_MERGE_ANY_ADS_CONFIGS,
        message: ''
      };

      await expectCustomHttpException(input);
    });

    it('should throw: 218 - cannot find any enabled adConfigs', async () => {
      const input: testParams2XXCodes = {
        params: { meta: { serviceId: '19', locationInfoSectionId: '1289' } },
        statusCode: CustomHttpStatus.CANNOT_FIND_ANY_ENABLED_ADCONFIGS,
        message:
          '6 adConfigs were found, but all of them are disabled. No enabled adConfigs matched the provided parameters - release: release/1.64.0/, pageType: story_story_video, sectionId: 1289, pageId: .'
      };

      await expectCustomHttpException(input);
    });
  });

  describe('Function retrieveDebugQueryParamOptions() helps with debug query params processing', () => {
    it('should return debug options - debug and omitCache are set to true', () => {
      const queryParam = 'true,omitCache';
      const result = service.retrieveDebugQueryParamOptions(queryParam);

      expect(result).toEqual({ debug: true, omitCache: true });
    });

    it('should return debug options - only omitCache is set to true', () => {
      const queryParam = 'false,omitCache';
      const result = service.retrieveDebugQueryParamOptions(queryParam);

      expect(result).toEqual({ debug: false, omitCache: true });
    });

    it('should return debug options - only debug is set to true', () => {
      const queryParam = 'true';
      const result = service.retrieveDebugQueryParamOptions(queryParam);

      expect(result).toEqual({ debug: true, omitCache: false });
    });

    it('should return debug options - debug and omitCache is set to false #1', () => {
      const queryParam = 'false';
      const result = service.retrieveDebugQueryParamOptions(queryParam);

      expect(result).toEqual({ debug: false, omitCache: false });
    });

    it('should return debug options - debug and omitCache is set to false #2', () => {
      const queryParam = undefined;
      const result = service.retrieveDebugQueryParamOptions(queryParam);

      expect(result).toEqual({ debug: false, omitCache: false });
    });

    it('should return debug options - debug and omitCache is set to false #2', () => {
      const queryParam = '';
      const result = service.retrieveDebugQueryParamOptions(queryParam);

      expect(result).toEqual({ debug: false, omitCache: false });
    });

    it('should return debug options - debug and omitCache is set to false #5', () => {
      const queryParam = 'apud';
      const result = service.retrieveDebugQueryParamOptions(queryParam);

      expect(result).toEqual({ debug: false, omitCache: false });
    });
  });

  describe('getRulesEngineRunResult', () => {
    it('should catch Engine error correctly', async () => {
      const err = new Error('some error');

      (service as any).engine = {
        run: async () => {
          throw err;
        }
      };

      expect(service['getRulesEngineRunResult']({} as any)).rejects.toThrow(
        CreateException({
          message: `Unknown Json Rules Engine result error. ${err}`,
          statusCode: CustomHttpStatus.ENGINE_RUN
        })
      );

      (service as any).engine = undefined;
    });
  });

  describe('matchPlaceholdersWithAdConfigs', () => {
    it('should return empty array when siteMapMatchedPlaceholders is nullable', () => {
      const siteMapMatchedPlaceholders = null as any;
      const availableAdConfigs: PlaceholdersDetails[] = [];
      const ruleAdConfigOverride = undefined;
      const eventPriority = 0;
      const rulePriority = 0;
      const priorityGroup = undefined;

      const result = service['matchPlaceholdersWithAdConfigs'](
        siteMapMatchedPlaceholders,
        availableAdConfigs,
        ruleAdConfigOverride,
        eventPriority,
        rulePriority,
        priorityGroup
      );
      expect(result).toEqual([]);
    });

    it.each`
      width   | height  | expected
      ${1}    | ${null} | ${[]}
      ${null} | ${1}    | ${[]}
    `(
      `should return empty array when width is $width and height is $height`,
      ({
        width,
        height,
        expected
      }: {
        width: number | null;
        height: number | null;
        expected: never[];
      }) => {
        const siteMapMatchedPlaceholders = {} as any;
        const ruleAdConfigOverride = undefined;
        const eventPriority = 0;
        const rulePriority = 0;
        const priorityGroup = undefined;
        const availableAdConfigs: PlaceholdersDetails[] = [
          {
            AD_Config_group: 'native',
            AD_Config_element_id: '1',
            width,
            height
          } as any
        ];

        const result = service['matchPlaceholdersWithAdConfigs'](
          siteMapMatchedPlaceholders,
          availableAdConfigs,
          ruleAdConfigOverride,
          eventPriority,
          rulePriority,
          priorityGroup
        );
        expect(result).toEqual(expected);
      }
    );
  });

  describe('omitCache', () => {
    it('should return correct response and skip cache', async () => {
      const cacheGetSpy = jest.spyOn(service['adConfigService']['cache'], 'get');
      const cacheManagerGetSpy = jest.spyOn(
        service['adConfigService']['cache']['cacheManager'],
        'get'
      );

      const result = await service.createResponse(body_1, true, true);
      checkResponse(result, outputMock_base);

      expect(cacheGetSpy).toHaveBeenCalledTimes(8);
      expect(cacheManagerGetSpy).toHaveBeenCalledTimes(0);

      cacheGetSpy.mockRestore();
      cacheManagerGetSpy.mockRestore();
    });
  });

  describe('handleRequestProcessingStats', () => {
    it('should throw error when failedPlaceholdersMerge is not empty and placeholdersWithHandledPrios is empty', () => {
      const placeholdersWithHandledPrios: IRulesPriorities[] = [];
      const failedPlaceholdersMerge: PlaceholderType[] = [{ type: 'placeholder', id: '1' }];
      const content = {} as any;
      const releaseVersion = {} as any;
      const configName = {} as any;
      const adsConfigs: PlaceholdersDetails[] = [];
      const rulesStats: RuleStats[] = [];

      expect(async () =>
        service['handleRequestProcessingStats'](
          placeholdersWithHandledPrios,
          failedPlaceholdersMerge,
          content,
          releaseVersion,
          configName,
          adsConfigs,
          rulesStats
        )
      ).rejects.toThrow(
        CreateException({
          message: 'Cannot merge any ads configs with placeholders',
          statusCode: CustomHttpStatus.CANNOT_MERGE_ANY_ADS_CONFIGS
        })
      );
    });

    it('should return correct debug data', () => {
      const placeholdersWithHandledPrios: IRulesPriorities[] = [
        { id: '1', AD_Config_group: 'A', AD_Config_element_id: 'B' }
      ] as any;
      const failedPlaceholdersMerge: PlaceholderType[] = [];
      const content = {
        type: 'C',
        meta: { serviceId: 'D', deviceType: 'E', locationInfoSectionId: 'F' }
      } as any;
      const releaseVersion = 'G';
      const configName = 'H';
      const adsConfigs: PlaceholdersDetails[] = [];
      const rulesStats: RuleStats[] = [];

      const result = service['handleRequestProcessingStats'](
        placeholdersWithHandledPrios,
        failedPlaceholdersMerge,
        content,
        releaseVersion,
        configName,
        adsConfigs,
        rulesStats
      );

      expect(result).toEqual({
        releaseVersion,
        allAvailableAdConfigGroups: [],
        allRulesCount: 0,
        successConditionsCount: 0,
        successEventsCount: 0,
        successMergeCount: 0,
        shortSuccessMergeStats: [{ placeholderId: '1', group: 'A', groupId: 'B' }],
        rulesStats: { success: [], fail: [] },
        reqBodyType: content.type,
        fullConfigName: configName
      });
    });
  });
});
