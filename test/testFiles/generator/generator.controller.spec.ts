import { createMock } from '@golevelup/ts-jest';
import { Test, TestingModule } from '@nestjs/testing';
import { AdConfigDeviceTypeEnum, CustomHttpStatus } from 'ads-layouts-tools';
import { FastifyReply, FastifyRequest } from 'fastify';
import { BodyToHeaderParse, ContentMeta, GeneratorQuery } from 'InterfacesAndTypes';
import { body_1 } from 'Mocks';
import { CacheModule } from 'src/cacheModule/cache.module';
import { GeneratorController } from 'src/generator/generator.controller';
import { GeneratorModule } from 'src/generator/generator.module';
import { GeneratorService } from 'src/generator/generator.service';
import { CreateException } from 'Utils';

jest.mock('Utils/logger');

describe('GeneratorModule', () => {
  let generatorController: GeneratorController;
  let mockedGeneratorService: jest.Mocked<GeneratorService>;
  const retrieveDebugQueryParamOptionsMock = jest.fn();

  beforeAll(async () => {
    mockedGeneratorService = {
      retrieveDebugQueryParamOptions: retrieveDebugQueryParamOptionsMock,
      createResponse: jest.fn()
    } as unknown as jest.Mocked<GeneratorService>;

    const app: TestingModule = await Test.createTestingModule({
      imports: [CacheModule.register({ isActive: false })],
      controllers: [GeneratorController],
      providers: [
        GeneratorModule,
        {
          provide: GeneratorService,
          useValue: mockedGeneratorService
        }
      ]
    }).compile();

    generatorController = app.get(GeneratorController);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(generatorController).toBeDefined();
  });

  it('should return correct response', async () => {
    retrieveDebugQueryParamOptionsMock.mockReturnValue({ debug: false });
    const req = { socket: {} } as unknown as FastifyRequest;
    const res = createMock() as unknown as FastifyReply;
    const query: GeneratorQuery = { debug: 'false' };
    const headers: BodyToHeaderParse<ContentMeta> = {
      'x-at-device-type': AdConfigDeviceTypeEnum.DESKTOP,
      'x-at-location-info-page-id': '7931689',
      'x-at-location-info-page-type': 'story_story_video',
      'x-at-location-info-section-id': '222',
      'x-at-location-info-section-name': 'Gwiazdy',
      'x-at-service-env': 'production',
      'x-at-service-id': 'ddtvn',
      'x-at-site-version': 'ab_atsdk_ga',
      'x-at-time': '1716539400000',
      'x-at-site-version-identifier':
        '7931689_20240524074854_222_aa6fa2ed0109e76f090e83ba88bc2d12'
    };

    const result = await generatorController.generate(req, body_1, query, res, headers);
  });

  it('should throw on headers validation', async () => {
    retrieveDebugQueryParamOptionsMock.mockReturnValue({ debug: false });
    const req = { socket: {} } as unknown as FastifyRequest;
    const res = createMock() as unknown as FastifyReply;
    const query: GeneratorQuery = { debug: 'false' };
    const headers = {} as BodyToHeaderParse<ContentMeta>;

    const expectedError = CreateException({
      statusCode: CustomHttpStatus.HEADERS_VALIDATION,
      message:
        'Headers validation error. "x-at-device-type" is required, "x-at-location-info-page-type" is required, "x-at-location-info-section-id" is required, "x-at-location-info-section-name" is required, "x-at-service-id" is required, "x-at-site-version-identifier" is required, "x-at-time" is required, "x-at-service-env" is required, "x-at-site-version" is required, "x-at-location-info-page-id" is required'
    });

    await expect(
      generatorController.generate(req, body_1, query, res, headers)
    ).rejects.toThrow(expectedError);
  });
});
